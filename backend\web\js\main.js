// Initialize Lucide icons only
document.addEventListener("DOMContentLoaded", function () {
  lucide.createIcons();
  initSearchHandler();
  initPasswordToggle();
});

// Handle PJAX reloads
$(document).on("pjax:complete", function () {
  lucide.createIcons();
  initSearchHandler();
});

// Generic Search Handler
function initSearchHandler2() {
  // Find all search inputs created by our widget
  document
    .querySelectorAll('.input-group input[type="search"]')
    .forEach((searchInput) => {
      if (!searchInput) return;

      let searchTimer;
      const paramName =
        searchInput.closest(".input-group").dataset.paramName ||
        "FunctionalitySearch[globalSearch]";

      // Remove existing event listeners to prevent duplicates
      $(searchInput).off("input keydown");

      // Handle escape key
      $(searchInput).on("keydown", function (e) {
        if (e.key === "Escape") {
          // Clear the input
          this.value = "";

          // Get current URL and remove the search parameter
          let url = new URL(window.location.href);
          url.searchParams.delete(paramName);

          // Update browser history without reloading
          window.history.pushState({}, "", url.toString());

          // Reload the grid
          $.pjax.reload({
            container: "#crud-datatable-pjax",
            url: url.toString(),
            type: "GET",
            timeout: 2000,
          });
        }
      });

      // Listen for any input changes (including deletions)
      $(searchInput).on("input", function () {
        clearTimeout(searchTimer);
        const currentSearchValue = this.value;

        searchTimer = setTimeout(() => {
          // Get current URL
          let url = new URL(window.location.href);

          // Always clear the existing search parameter first
          url.searchParams.delete(paramName);

          // Only add the parameter if there's actually a search value
          if (currentSearchValue.trim() !== "") {
            url.searchParams.set(paramName, currentSearchValue);
          }

          // Update browser history
          window.history.pushState({}, "", url.toString());

          // Reload the grid with the new URL
          $.pjax
            .reload({
              container: "#crud-datatable-pjax",
              url: url.toString(),
              type: "GET",
              timeout: 2000,
            })
            .done(function () {
              // Restore focus and value after reload
              const searchInput = document.querySelector(
                `input[type="search"][id="${searchInput.id}"]`
              );
              if (searchInput) {
                searchInput.value = currentSearchValue;
                searchInput.focus();
              }
            });
        }, 300);
      });
    });
}

function debounce(func, delay) {
  let timer;
  return function (...args) {
    clearTimeout(timer);
    timer = setTimeout(() => {
      func.apply(this, args);
    }, delay);
  };
}

function handleSearchInput(event) {
  const input = event.target;
  const value = input.value.trim();
  const id = input.id;
  const paramName = input.getAttribute("data-param-name"); // Example: FunctionalitySearch[globalSearch]
  const modelPrefix = input.getAttribute("data-model-prefix"); // Example: FunctionalitySearch

  console.log(`Debounced Search Input:
    ID: ${id}
    Value: ${value}
    Param Name: ${paramName}
    Model Prefix: ${modelPrefix}
  `);

  // --- Update URL ---
  let url = new URL(window.location.href);

  // Break down paramName to get actual key names
  let paramKey = paramName.replace("[", "%5B").replace("]", "%5D");

  // Remove existing param
  url.searchParams.delete(`${modelPrefix}[globalSearch]`); // Optional: remove old

  if (value !== "") {
    // Add updated param
    url.searchParams.set(`${modelPrefix}[globalSearch]`, value);
  }

  // Update browser history without reload
  window.history.pushState({}, "", url.toString());

  // --- Optional: Reload PJAX grid ---
  $.pjax.reload({
    container: "#crud-datatable-pjax", // Change if needed
    url: url.toString(),
    type: "GET",
    timeout: 2000,
  });
}

function initSearchHandler() {
  const searchInputs = document.querySelectorAll(".record-search-input");

  searchInputs.forEach((input) => {
    // Remove existing event listeners (if PJAX reload)
    $(input).off("input");

    // Attach debounced input listener
    $(input).on("input", debounce(handleSearchInput, 300)); // 300ms debounce
  });
}

// Password Visibility Toggle Handler
function initPasswordToggle() {
  document.querySelectorAll(".toggle-password").forEach(function (toggle) {
    toggle.addEventListener("click", function () {
      const input = this.parentElement.querySelector("input");
      const icon = this.querySelector("[data-lucide]");

      if (input.type === "password") {
        input.type = "text";
        icon.removeAttribute("data-lucide");
        icon.setAttribute("data-lucide", "eye-off");
      } else {
        input.type = "password";
        icon.removeAttribute("data-lucide");
        icon.setAttribute("data-lucide", "eye");
      }

      // Remove the old icon and recreate it
      icon.innerHTML = "";
      lucide.createIcons({
        elements: [icon],
      });
    });
  });
}

// Initialize search function immediately
initSearchHandler();

$(document).on("click", ".notification-link", function (e) {
  e.preventDefault();

  const id = $(this).data("id");
  const link = $(this).data("link");

  if (!id) {
    console.error("Missing notification ID");
    return;
  }

  $.ajax({
    url: "/backoffice/site/mark-notification-as-read",
    type: "POST",
    data: {
      id: id,
      _csrf: yii.getCsrfToken(),
    },
    success: function (res) {
      if (res.success) {
        if (link && link !== "" && link !== "undefined") {
          window.location.href = link;
        } else {
          // Reload the page to update notification count
          window.location.reload();
        }
      } else {
        console.error("Failed to mark as read: " + (res.error || ""));
        alert(
          "Failed to mark notification as read: " +
            (res.error || "Unknown error")
        );
      }
    },
    error: function (xhr, status, error) {
      console.error("AJAX error:", status, error);
      console.error("Response:", xhr.responseText);
      alert("Error marking notification as read. Please try again.");
    },
  });
});
