<?php

use common\models\Role;
use kartik\form\ActiveForm;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
?>

<div class="user-form">
    <?php $form = ActiveForm::begin([
        'id' => 'user-form',
        'enableAjaxValidation' => true,
        'validationUrl' => ['user/validate'],
    ]); ?>

    <?= $form->field($model, 'username')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'email')->textInput(['maxlength' => true]) ?>

    <?php if ($model->isNewRecord): ?>
        <?= $form->field($model, 'password')->passwordInput() ?>
    <?php else: ?>
        <div class="form-group">
            <?= Html::button('Send Password Reset Email', [
                'class' => 'btn btn-sm btn-warning',
                'onclick' => 'sendPasswordReset("' . $model->userId . '")',
            ]) ?>
        </div>
    <?php endif; ?>

    <?= $form->field($model, 'status')->dropDownList([
        10 => 'Active',
        9 => 'Inactive',
        0 => 'Deleted',
    ]) ?>

    <?= $form->field($model, 'role_id')->widget(Select2::classname(), [
        'data' => ArrayHelper::map(Role::find()->all(), 'id', 'name'),
        'options' => [
            'placeholder' => 'Select a role...',
        ],
        'pluginOptions' => [
            'allowClear' => true,
            'dropdownParent' => '#ajaxCrudModal'
        ]
    ]); ?>

    <?php if (!Yii::$app->request->isAjax) { ?>
        <div class="form-group">
            <?= Html::submitButton(
                $model->isNewRecord ? 'Create' : 'Update',
                ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']
            ) ?>
        </div>
    <?php } ?>

    <?php ActiveForm::end(); ?>
</div>

<?php
$script = <<<JS
function sendPasswordReset(userId) {
    if (confirm('Are you sure you want to send a password reset email to this user?')) {
        $.ajax({
            url: '/admin/user/send-password-reset',
            type: 'POST',
            data: {
                id: userId,
                _csrf: yii.getCsrfToken()
            },
            success: function(response) {
                if (response.success) {
                    krajeeDialog.alert('Password reset email has been sent successfully.');
                } else {
                    krajeeDialog.alert('Failed to send password reset email: ' + response.message);
                }
            },
            error: function() {
                krajeeDialog.alert('An error occurred while sending the password reset email.');
            }
        });
    }
}

// Add form submit handler
$('#user-form').on('beforeSubmit', function(e) {
    var form = $(this);
    var submitBtn = form.find(':submit');
    submitBtn.prop('disabled', true);
    
    $.ajax({
        url: form.attr('action'),
        type: 'POST',
        data: form.serialize(),
        success: function(response) {
            if (response.forceReload) {
                $.pjax.reload({container: response.forceReload});
                $('#modal').modal('hide');
            } else {
                $('#modal').find('.modal-body').html(response.content);
            }
        },
        error: function() {
            krajeeDialog.alert('An error occurred while saving the user.');
        },
        complete: function() {
            submitBtn.prop('disabled', false);
        }
    });
    return false;
});
JS;
$this->registerJs($script);
?>