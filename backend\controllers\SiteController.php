<?php

namespace backend\controllers;

use bedezign\yii2\audit\Audit;
use bedezign\yii2\audit\models\AuditEntry;
use common\models\Afspraak;
use common\models\Document;
use common\models\DocumentType;
use common\models\LoginForm;
use common\models\User;
use Yii;
use yii\filters\VerbFilter;
use yii\filters\AccessControl;
use yii\web\Controller;
use yii\web\Response;

/**
 * Site controller
 */
class SiteController extends Controller
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::class,
                'rules' => [
                    [
                        'actions' => ['login', 'error'],
                        'allow' => true,
                    ],
                    [
                        'actions' => ['logout', 'index', 'mark-notification-as-read'],
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                ],
            ],
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'logout' => ['post'],
                    'mark-notification-as-read' => ['post'],
                ],
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function actions()
    {
        return [
            'error' => [
                'class' => \yii\web\ErrorAction::class,
            ],
        ];
    }

    /**
     * Displays homepage.
     *
     * @return string
     */
    public function actionIndex()
    {
        // Chart 1: Documents grouped by status
        $documentsByStatus = Document::find()
            ->select(['status', 'COUNT(*) as total'])
            ->groupBy('status')
            ->asArray()
            ->all();

        $statusLabels = array_column($documentsByStatus, 'status');

        // Remove "document-workflow/" from status labels
        foreach ($statusLabels as $key => $status) {
            $statusLabels[$key] = str_replace('document-workflow/', '', $status);
        }

        $statusData = array_map('intval', array_column($documentsByStatus, 'total'));

        // Chart 2: Documents grouped by type
        $documentsByType = Document::find()
            ->alias('d')
            ->select(['dt.type as type', 'COUNT(*) as total'])
            ->leftJoin('document_type dt', 'dt.id = d.documenttype_id')
            ->groupBy('d.documenttype_id')
            ->asArray()
            ->all();

        $typeData = [];
        foreach ($documentsByType as $row) {
            $typeData[] = [
                'name' => $row['type'] ?? 'Unknown',
                'y' => (int) $row['total']
            ];
        }

        // Recent logins (from audit)
        $recentLogins = AuditEntry::find()
            ->where(['route' => 'site/login'])
            ->andWhere(['IS NOT', 'user_id', null]) // exclude guests
            ->orderBy(['created' => SORT_DESC])
            ->limit(8)
            ->all();

        // Summary cards
        $totalDocuments = Document::find()->count();
        $totalUsers = User::find()->count();
        $totalTypes = DocumentType::find()->count();
        $appointmentsToday = Afspraak::find()->where(['date' => date('Y-m-d')])->count();

        return $this->render('index', [
            'statusLabels' => $statusLabels,
            'statusData' => $statusData,
            'typeData' => $typeData,
            'recentLogins' => $recentLogins,
            'totalDocuments' => $totalDocuments,
            'totalUsers' => $totalUsers,
            'totalTypes' => $totalTypes,
            'appointmentsToday' => $appointmentsToday,
        ]);
        // return $this->render('index');
    }

    /**
     * Login action.
     *
     * @return string|Response
     */
    public function actionLogin()
    {
        if (!Yii::$app->user->isGuest) {
            return $this->goHome();
        }

        $this->layout = 'blank';

        $model = new LoginForm();
        if ($model->load(Yii::$app->request->post()) && $model->login()) {
            return $this->goBack();
        }

        $model->password = '';

        return $this->render('login', [
            'model' => $model,
        ]);
    }

    /**
     * Logout action.
     *
     * @return Response
     */
    public function actionLogout()
    {
        Yii::$app->user->logout();

        return $this->goHome();
    }

    /**
     * Mark notification as read action.
     *
     * @return array
     */
    public function actionMarkNotificationAsRead()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        $id = Yii::$app->request->post('id');

        if (!$id) {
            Yii::error('Mark notification as read: Missing ID', __METHOD__);
            return ['success' => false, 'error' => 'Missing ID'];
        }

        $notification = \common\models\NotificationUser::findOne([
            'id' => $id,
            'user_id' => Yii::$app->user->id,
        ]);

        if (!$notification) {
            Yii::warning("Notification not found: ID={$id}, User=" . Yii::$app->user->id, __METHOD__);
            return ['success' => false, 'error' => 'Notification not found'];
        }

        // Mark as read even if already read (idempotent operation)
        $notification->is_read = 1;
        if (!$notification->save(false)) {
            Yii::error('Failed to save notification: ' . json_encode($notification->errors), __METHOD__);
            return ['success' => false, 'error' => 'Failed to save notification'];
        }

        Yii::info("Notification marked as read: ID={$id}", __METHOD__);
        return ['success' => true];
    }
}
