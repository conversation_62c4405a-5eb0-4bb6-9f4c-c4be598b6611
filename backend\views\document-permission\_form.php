<?php

use kartik\switchinput\SwitchInput;
use yii\helpers\Html;
use yii\bootstrap5\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\DocumentPermission */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="document-permission-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->field($model, 'document_id')->textInput([
        'value' => $model->document ? $model->document->filename : $model->document_id,
        'readonly' => true,
        // 'class' => 'form-control-plaintext'
    ]) ?>

    <?= $form->field($model, 'user_id')->textInput([
        'value' => $model->user ? $model->user->username : $model->user_id,
        'readonly' => true,
        // 'class' => 'form-control-plaintext'
    ]) ?>

    <?= Html::hiddenInput('DocumentPermission[document_id]', $model->document_id) ?>
    <?= Html::hiddenInput('DocumentPermission[user_id]', $model->user_id) ?>

    <?= $form->field($model, 'can_view')->widget(SwitchInput::classname(), [
        'type' => SwitchInput::CHECKBOX,
    ]) ?>

    <?php if (!Yii::$app->request->isAjax) { ?>
        <div class="form-group">
            <?= Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
        </div>
    <?php } ?>

    <?php ActiveForm::end(); ?>

</div>