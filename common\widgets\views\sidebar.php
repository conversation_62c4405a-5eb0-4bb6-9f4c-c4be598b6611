<?php

use common\models\DocumentType;
use yii\helpers\Url;
use common\models\MenuItem;

$location = Yii::$app->id === 'app-frontend' ? 'frontend' : 'backend';

$menuItems = [];

if (!Yii::$app->user->isGuest) {
    $rawMenuItems = MenuItem::find()
        ->where(['visible' => 1])
        ->andWhere(['or', ['location' => $location], ['location' => 'both']])
        ->orderBy(['sort_order' => SORT_ASC])
        ->asArray()
        ->all();

    $menuItems = buildMenuTree($rawMenuItems);

    // Add "Nieuw Rapport" section for frontend
    if ($location === 'frontend' && !Yii::$app->user->isGuest && Yii::$app->user->identity->role->instantie_id == 1) {
        $menuItems[] = [
            'label' => 'Nieuw Rapport',
            'header' => true,
        ];

        // Get current route and parameters
        $currentRoute = Yii::$app->controller->route;
        $currentDocId = Yii::$app->request->get('document_id');

        // Fetch all document names
        $documents = DocumentType::find()->all();

        foreach ($documents as $document) {
            $menuItems[] = [
                'label' => $document->type,
                'url' => ['/document/create', 'document_id' => $document->id],
                'icon' => 'file-plus',
                'iconType' => 'lucide',
                'active' => ($currentRoute === 'document/create' && $currentDocId == $document->id),
            ];
        }
    }
}
// Only process role functionalities if user is logged in
// if (!Yii::$app->user->isGuest) {
//     $userRoleId = Yii::$app->user->identity->role_id;

//     // Fetch functionalities for the current user's role
//     $roleFunctionalities = RoleFunctionality::find()
//         ->with('functionality')  // eager loading the functionality relation
//         ->where(['role_id' => $userRoleId])
//         ->all();

//     // Only add the Features section if there are functionalities
//     if (!empty($roleFunctionalities)) {
//         $menuItems[] = [
//             'label' => 'Features',
//             'header' => true,
//         ];

//         // Track unique routes
//         $uniqueRoutes = [];

//         // Add menu items based on functionalities
//         foreach ($roleFunctionalities as $roleFunc) {
//             if ($roleFunc->functionality) {
//                 $route = $roleFunc->functionality->controllerRoute;

//                 // Only add if this route hasn't been added before
//                 if (!in_array($route, $uniqueRoutes)) {
//                     $uniqueRoutes[] = $route;

//                     // Get the base name (without the action part)
//                     $baseName = trim(explode('-', $roleFunc->functionality->name)[0]);

//                     $menuItems[] = [
//                         'label' => $baseName,
//                         'url' => [$route],
//                         'icon' => 'file-symlink',
//                         'iconType' => 'lucide',
//                         'activeMatch' => 'exact',
//                     ];
//                 }
//             }
//         }
//     }

//     // Add About and Contact for logged-in users
//     // $menuItems[] = [
//     //     'label' => 'Other Pages',
//     //     'header' => 'true'
//     // ];
//     // $menuItems[] = [
//     //     'label' => 'About',
//     //     'url' => ['/site/about'],
//     //     'icon' => 'info',
//     //     'iconType' => 'lucide',
//     // ];

//     // $menuItems[] = [
//     //     'label' => 'Contact',
//     //     'url' => ['/site/contact'],
//     //     'icon' => 'mail',
//     //     'iconType' => 'lucide',
//     // ];
// } else {
//     // Menu items for guests
//     $menuItems[] = [
//         'label' => 'Login',
//         'url' => ['/site/login'],
//         'icon' => 'log-in',
//         'iconType' => 'lucide',
//     ];
// }

if (Yii::$app->user->isGuest) {
    $menuItems[] = [
        'label' => 'Login',
        'url' => ['/site/login'],
        'icon' => 'log-in',
        'iconType' => 'lucide',
    ];
}

?>
<aside class="main-sidebar bg-white elevation-4 rounded-end-3">
    <!-- Brand Logo -->
    <a href="<?= Url::home() ?>" class="brand-link text-decoration-none text-center">
        <span class="brand-text fw-semibold"><?= Yii::$app->name ?></span>
    </a>

    <!-- Sidebar -->
    <div class="sidebar">
        <!-- Sidebar Menu -->
        <nav class="mt-4">
            <?= \common\components\LucideSidebarMenuHelper::widget([
                'items' => $menuItems
            ]) ?>
        </nav>
    </div>
</aside>