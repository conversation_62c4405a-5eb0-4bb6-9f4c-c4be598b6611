<?php

use yii\base\Event;
use yii\web\Controller;

Yii::set<PERSON><PERSON><PERSON>('@common', dirname(__DIR__));
Yii::set<PERSON>lia<PERSON>('@frontend', dirname(dirname(__DIR__)) . '/frontend');
Yii::set<PERSON>lia<PERSON>('@backend', dirname(dirname(__DIR__)) . '/backend');
Yii::set<PERSON>lia<PERSON>('@console', dirname(dirname(__DIR__)) . '/console');

require_once __DIR__ . '/../helpers/functions.php';
require_once __DIR__ . '/../helpers/canAccessHelper.php';
require_once __DIR__ . '/../helpers/workflowHelper.php';
// require_once __DIR__ . '/../components/UserIdentity.php';

Event::on(Controller::class, Controller::EVENT_BEFORE_ACTION, function ($event) {
    $route = '/' . $event->action->controller->id . '/' . $event->action->id;
    $request = Yii::$app->request;

    // Skip some routes
    $skip = ['/site/error', '/debug/default/toolbar', '/debug/default/view'];
    if (in_array($route, $skip)) {
        return;
    }

    Yii::$app->notificationManager->checkAndTrigger($route, $request);
});

Event::on(Controller::class, Controller::EVENT_AFTER_ACTION, function ($event) {
    $route = '/' . $event->action->controller->id . '/' . $event->action->id;
    $request = Yii::$app->request;

    $skip = ['/site/error', '/debug/default/toolbar', '/debug/default/view'];
    if (in_array($route, $skip)) {
        return;
    }

    $extraParams = [];
    if (isset(Yii::$app->params['lastCreatedId'])) {
        $extraParams['id'] = Yii::$app->params['lastCreatedId'];
        unset(Yii::$app->params['lastCreatedId']); // cleanup
    }

    Yii::$app->notificationManager->checkAndTrigger($route, $request, 'after', $extraParams);
});
