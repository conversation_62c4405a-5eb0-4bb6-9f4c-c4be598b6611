<?php

use milos<PERSON>man\highcharts\Highcharts;

$this->title = 'Dashboard';
$this->params['breadcrumbs'] = [['label' => $this->title]];
?>
<div class="container-fluid">
    <!-- Welcome back user -->
    <div class="row my-3">
        <div class="col-6">
            <h4>Welcome terug,
                <strong class="text-primary">
                    <?= Yii::$app->user->identity->username ?>
                </strong>
            </h4>

        </div>
        <div class="col-6 d-d-inline-flex justify-content-end align-items-center">
            <p class="text-end fw-medium">
                <i data-lucide="calendar" class="icon-class"></i>
                <?= Yii::$app->formatter->asDate('now', 'php:l, d F Y') ?>
            </p>
        </div>
    </div>

    <!-- Summary cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card shadow-sm p-3 rounded-3 hover-card">
                <h5>Total Documents</h5>
                <h2><?= $totalDocuments ?></h2>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card shadow-sm p-3 rounded-3 hover-card">
                <h5>Total Users</h5>
                <h2><?= $totalUsers ?></h2>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card shadow-sm p-3 rounded-3 hover-card">
                <h5>Total Document Types</h5>
                <h2><?= $totalTypes ?></h2>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card shadow-sm p-3 rounded-3 hover-card">
                <h5>Total Appointments today</h5>
                <h2><?= $appointmentsToday ?></h2>
            </div>
        </div>
    </div>


    <div class="row">
        <!-- Documents by Status -->
        <div class="col-md-6">
            <div class="card card-body shadow-sm rounded-3 hover-card">
                <!-- <h4>Documents by Status</h4> -->
                <?= Highcharts::widget([
                    'options' => [
                        'chart' => ['type' => 'column'],
                        'title' => ['text' => 'Documents by Status'],
                        'xAxis' => ['categories' => $statusLabels],
                        'yAxis' => ['title' => ['text' => 'Count']],
                        'series' => [[
                            'name' => 'Documents',
                            'data' => $statusData
                        ]]
                    ]
                ]); ?>
            </div>
        </div>

        <!-- Documents by Type -->
        <div class="col-md-6">
            <div class="card card-body shadow-sm rounded-3 hover-card">
                <!-- <h4>Documents by Type</h4> -->
                <?= Highcharts::widget([
                    'options' => [
                        'chart' => ['type' => 'pie'],
                        'title' => ['text' => 'Documents by Type'],
                        'series' => [[
                            'name' => 'Documents',
                            'colorByPoint' => true,
                            'data' => $typeData
                        ]]
                    ]
                ]); ?>
            </div>
        </div>
    </div>

    <!-- Recent Logins -->
    <?php if (isHoofdOrOnderhoofd()): ?>
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card card-body shadow-sm rounded-3 hover-card">
                    <h4>Recent Logins</h4>
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>IP Address</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recentLogins as $login): ?>
                                <tr>
                                    <td><?= $login->user_id ? Yii::$app->user->identityClass::findOne($login->user_id)->username : 'Guest' ?></td>
                                    <td><?= $login->ip ?></td>
                                    <td><?= Yii::$app->formatter->asDatetime($login->created) ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>