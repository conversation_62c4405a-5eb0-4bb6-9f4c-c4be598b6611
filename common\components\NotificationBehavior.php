<?php

namespace common\components;

use Yii;
use yii\base\Behavior;
use yii\db\ActiveRecord;

class NotificationBehavior extends Behavior
{
    /**
     * @inheritdoc
     */
    public function events()
    {
        // Listen for the events we care about
        return [
            ActiveRecord::EVENT_AFTER_INSERT => 'handleModelEvent',
            ActiveRecord::EVENT_AFTER_UPDATE => 'handleModelEvent',
            // You could also add ActiveRecord::EVENT_AFTER_DELETE
        ];
    }

    /**
     * This method is called automatically when one of the above events is triggered.
     * @param \yii\base\Event $event
     */
    public function handleModelEvent($event)
    {
        /** @var ActiveRecord $model */
        $model = $this->owner; // The model this behavior is attached to
        $eventName = $event->name; // 'afterInsert' or 'afterUpdate'

        // Call our new NotificationManager method
        Yii::$app->notificationManager->triggerByModelEvent($model, $eventName);
    }
}
