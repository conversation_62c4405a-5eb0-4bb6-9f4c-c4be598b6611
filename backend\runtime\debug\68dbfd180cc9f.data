a:14:{s:6:"config";s:15911:"a:5:{s:10:"phpVersion";s:5:"8.3.3";s:10:"yiiVersion";s:6:"2.0.53";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.53";s:4:"name";s:3:"FMZ";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.3.3";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:71:{s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:47:"C:\Web\Reclassering\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:57:"C:\Web\Reclassering\vendor/yiisoft/yii2-symfonymailer/src";}}s:29:"hail812/yii2-adminlte-widgets";a:3:{s:4:"name";s:29:"hail812/yii2-adminlte-widgets";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:25:"@hail812/adminlte/widgets";s:60:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte-widgets/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.12.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap4/src";}}s:22:"hail812/yii2-adminlte3";a:3:{s:4:"name";s:22:"hail812/yii2-adminlte3";s:7:"version";s:7:"1.1.9.0";s:5:"alias";a:1:{s:18:"@hail812/adminlte3";s:53:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte3/src";}}s:18:"kartik-v/yii2-mpdf";a:3:{s:4:"name";s:18:"kartik-v/yii2-mpdf";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:12:"@kartik/mpdf";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-mpdf/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-dialog/src";}}s:23:"kartik-v/yii2-popover-x";a:3:{s:4:"name";s:23:"kartik-v/yii2-popover-x";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:15:"@kartik/popover";s:54:"C:\Web\Reclassering\vendor/kartik-v/yii2-popover-x/src";}}s:21:"kartik-v/yii2-builder";a:3:{s:4:"name";s:21:"kartik-v/yii2-builder";s:7:"version";s:7:"1.6.9.0";s:5:"alias";a:1:{s:15:"@kartik/builder";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-builder/src";}}s:22:"kartik-v/yii2-editable";a:3:{s:4:"name";s:22:"kartik-v/yii2-editable";s:7:"version";s:7:"1.8.0.0";s:5:"alias";a:1:{s:16:"@kartik/editable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-editable/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:7:"3.5.3.0";s:5:"alias";a:1:{s:12:"@kartik/grid";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-grid/src";}}s:21:"kartik-v/yii2-helpers";a:3:{s:4:"name";s:21:"kartik-v/yii2-helpers";s:7:"version";s:7:"1.3.9.0";s:5:"alias";a:1:{s:15:"@kartik/helpers";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-helpers/src";}}s:24:"kartik-v/yii2-checkbox-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-checkbox-x";s:7:"version";s:7:"1.0.7.0";s:5:"alias";a:1:{s:16:"@kartik/checkbox";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-checkbox-x/src";}}s:26:"kartik-v/yii2-context-menu";a:3:{s:4:"name";s:26:"kartik-v/yii2-context-menu";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:13:"@kartik/cmenu";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-context-menu/src";}}s:25:"kartik-v/yii2-datecontrol";a:3:{s:4:"name";s:25:"kartik-v/yii2-datecontrol";s:7:"version";s:7:"1.9.9.0";s:5:"alias";a:1:{s:19:"@kartik/datecontrol";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-datecontrol/src";}}s:22:"kartik-v/yii2-sortable";a:3:{s:4:"name";s:22:"kartik-v/yii2-sortable";s:7:"version";s:7:"1.2.2.0";s:5:"alias";a:1:{s:16:"@kartik/sortable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable/src";}}s:25:"kartik-v/yii2-field-range";a:3:{s:4:"name";s:25:"kartik-v/yii2-field-range";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:13:"@kartik/field";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-field-range/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-httpclient/src";}}s:25:"kartik-v/yii2-detail-view";a:3:{s:4:"name";s:25:"kartik-v/yii2-detail-view";s:7:"version";s:7:"1.8.7.0";s:5:"alias";a:1:{s:14:"@kartik/detail";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-detail-view/src";}}s:24:"kartik-v/yii2-date-range";a:3:{s:4:"name";s:24:"kartik-v/yii2-date-range";s:7:"version";s:7:"1.7.3.0";s:5:"alias";a:1:{s:17:"@kartik/daterange";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-date-range/src";}}s:22:"kartik-v/yii2-dynagrid";a:3:{s:4:"name";s:22:"kartik-v/yii2-dynagrid";s:7:"version";s:7:"1.5.5.0";s:5:"alias";a:1:{s:16:"@kartik/dynagrid";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-dynagrid/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:7:"2.0.7.0";s:5:"alias";a:1:{s:8:"@yii/jui";s:43:"C:\Web\Reclassering\vendor/yiisoft/yii2-jui";}}s:27:"kartik-v/yii2-label-inplace";a:3:{s:4:"name";s:27:"kartik-v/yii2-label-inplace";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/label";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-label-inplace/src";}}s:19:"kartik-v/yii2-icons";a:3:{s:4:"name";s:19:"kartik-v/yii2-icons";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/icons";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-icons/src";}}s:19:"kartik-v/yii2-money";a:3:{s:4:"name";s:19:"kartik-v/yii2-money";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/money";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-money/src";}}s:20:"kartik-v/yii2-ipinfo";a:3:{s:4:"name";s:20:"kartik-v/yii2-ipinfo";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/ipinfo";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-ipinfo/src";}}s:22:"kartik-v/yii2-markdown";a:3:{s:4:"name";s:22:"kartik-v/yii2-markdown";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/markdown";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-markdown/src";}}s:24:"kartik-v/yii2-dropdown-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-dropdown-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/dropdown";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-dropdown-x/src";}}s:19:"kartik-v/yii2-nav-x";a:3:{s:4:"name";s:19:"kartik-v/yii2-nav-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:11:"@kartik/nav";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-nav-x/src";}}s:22:"kartik-v/yii2-password";a:3:{s:4:"name";s:22:"kartik-v/yii2-password";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/password";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-password/src";}}s:20:"kartik-v/yii2-slider";a:3:{s:4:"name";s:20:"kartik-v/yii2-slider";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/slider";s:47:"C:\Web\Reclassering\vendor/kartik-v/yii2-slider";}}s:28:"kartik-v/yii2-sortable-input";a:3:{s:4:"name";s:28:"kartik-v/yii2-sortable-input";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:17:"@kartik/sortinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable-input/src";}}s:20:"kartik-v/yii2-tabs-x";a:3:{s:4:"name";s:20:"kartik-v/yii2-tabs-x";s:7:"version";s:7:"1.2.9.0";s:5:"alias";a:1:{s:12:"@kartik/tabs";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-tabs-x/src";}}s:30:"kartik-v/yii2-widget-typeahead";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-typeahead";s:7:"version";s:7:"1.0.4.0";s:5:"alias";a:1:{s:17:"@kartik/typeahead";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-typeahead/src";}}s:20:"kartik-v/yii2-social";a:3:{s:4:"name";s:20:"kartik-v/yii2-social";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:14:"@kartik/social";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-social/src";}}s:30:"kartik-v/yii2-widget-touchspin";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-touchspin";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:17:"@kartik/touchspin";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-touchspin/src";}}s:32:"kartik-v/yii2-widget-switchinput";a:3:{s:4:"name";s:32:"kartik-v/yii2-widget-switchinput";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:19:"@kartik/switchinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-switchinput";}}s:24:"kartik-v/yii2-validators";a:4:{s:4:"name";s:24:"kartik-v/yii2-validators";s:7:"version";s:7:"1.0.3.0";s:5:"alias";a:1:{s:18:"@kartik/validators";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-validators/src";}s:9:"bootstrap";s:27:"kartik\validators\Bootstrap";}s:28:"kartik-v/yii2-widget-spinner";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-spinner";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/spinner";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-spinner/src";}}s:28:"kartik-v/yii2-widget-sidenav";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-sidenav";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/sidenav";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-sidenav";}}s:27:"kartik-v/yii2-widget-rating";a:3:{s:4:"name";s:27:"kartik-v/yii2-widget-rating";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:14:"@kartik/rating";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rating/src";}}s:31:"kartik-v/yii2-widget-rangeinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-rangeinput";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:13:"@kartik/range";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rangeinput/src";}}s:26:"kartik-v/yii2-widget-growl";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-growl";s:7:"version";s:7:"1.1.2.0";s:5:"alias";a:1:{s:13:"@kartik/growl";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-growl/src";}}s:28:"kartik-v/yii2-widget-depdrop";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-depdrop";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:15:"@kartik/depdrop";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-depdrop/src";}}s:31:"kartik-v/yii2-widget-colorinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-colorinput";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:13:"@kartik/color";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-colorinput/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:7:"1.5.1.0";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:66:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:31:"kartik-v/yii2-widget-datepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-datepicker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:12:"@kartik/date";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datepicker/src";}}s:26:"kartik-v/yii2-widget-alert";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-alert";s:7:"version";s:7:"1.1.5.0";s:5:"alias";a:1:{s:13:"@kartik/alert";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-alert/src";}}s:26:"kartik-v/yii2-widget-affix";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-affix";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:13:"@kartik/affix";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-affix";}}s:21:"kartik-v/yii2-widgets";a:3:{s:4:"name";s:21:"kartik-v/yii2-widgets";s:7:"version";s:7:"3.4.1.0";s:5:"alias";a:1:{s:15:"@kartik/widgets";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-widgets/src";}}s:33:"kartik-v/yii2-bootstrap5-dropdown";a:3:{s:4:"name";s:33:"kartik-v/yii2-bootstrap5-dropdown";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:19:"@kartik/bs5dropdown";s:64:"C:\Web\Reclassering\vendor/kartik-v/yii2-bootstrap5-dropdown/src";}}s:26:"biladina/yii2-ajaxcrud-bs4";a:4:{s:4:"name";s:26:"biladina/yii2-ajaxcrud-bs4";s:7:"version";s:7:"3.0.0.0";s:5:"alias";a:1:{s:22:"@yii2ajaxcrud/ajaxcrud";s:57:"C:\Web\Reclassering\vendor/biladina/yii2-ajaxcrud-bs4/src";}s:9:"bootstrap";s:31:"yii2ajaxcrud\ajaxcrud\Bootstrap";}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@kartik/select2";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-select2/src";}}s:31:"kartik-v/yii2-widget-activeform";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-activeform";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/form";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-activeform/src";}}s:23:"raoul2000/yii2-workflow";a:3:{s:4:"name";s:23:"raoul2000/yii2-workflow";s:7:"version";s:7:"1.2.0.0";s:5:"alias";a:1:{s:19:"@raoul2000/workflow";s:54:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow/src";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:20:"kartik-v/yii2-export";a:3:{s:4:"name";s:20:"kartik-v/yii2-export";s:7:"version";s:7:"1.4.3.0";s:5:"alias";a:1:{s:14:"@kartik/export";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-export/src";}}s:28:"raoul2000/yii2-workflow-view";a:3:{s:4:"name";s:28:"raoul2000/yii2-workflow-view";s:7:"version";s:7:"0.0.2.0";s:5:"alias";a:1:{s:24:"@raoul2000/workflow/view";s:59:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow-view/src";}}s:32:"cornernote/yii2-workflow-manager";a:3:{s:4:"name";s:32:"cornernote/yii2-workflow-manager";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:28:"@cornernote/workflow/manager";s:63:"C:\Web\Reclassering\vendor/cornernote/yii2-workflow-manager/src";}}s:28:"2amigos/yii2-ckeditor-widget";a:3:{s:4:"name";s:28:"2amigos/yii2-ckeditor-widget";s:7:"version";s:7:"2.1.0.0";s:5:"alias";a:1:{s:19:"@dosamigos/ckeditor";s:59:"C:\Web\Reclassering\vendor/2amigos/yii2-ckeditor-widget/src";}}s:17:"yiisoft/yii2-twig";a:3:{s:4:"name";s:17:"yiisoft/yii2-twig";s:7:"version";s:7:"2.5.1.0";s:5:"alias";a:1:{s:9:"@yii/twig";s:48:"C:\Web\Reclassering\vendor/yiisoft/yii2-twig/src";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-debug/src";}}s:27:"2amigos/yii2-chartjs-widget";a:3:{s:4:"name";s:27:"2amigos/yii2-chartjs-widget";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:18:"@dosamigos/chartjs";s:58:"C:\Web\Reclassering\vendor/2amigos/yii2-chartjs-widget/src";}}s:19:"bedezign/yii2-audit";a:4:{s:4:"name";s:19:"bedezign/yii2-audit";s:7:"version";s:7:"1.2.7.0";s:5:"alias";a:1:{s:20:"@bedezign/yii2/audit";s:50:"C:\Web\Reclassering\vendor/bedezign/yii2-audit/src";}s:9:"bootstrap";s:29:"bedezign\yii2\audit\Bootstrap";}s:30:"kartik-v/yii2-widget-fileinput";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-fileinput";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/file";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-fileinput/src";}}s:24:"rmrevin/yii2-fontawesome";a:3:{s:4:"name";s:24:"rmrevin/yii2-fontawesome";s:7:"version";s:8:"2.10.3.0";s:5:"alias";a:1:{s:24:"@rmrevin/yii/fontawesome";s:51:"C:\Web\Reclassering\vendor/rmrevin/yii2-fontawesome";}}s:24:"edofre/yii2-fullcalendar";a:3:{s:4:"name";s:24:"edofre/yii2-fullcalendar";s:7:"version";s:8:"1.0.11.0";s:5:"alias";a:1:{s:20:"@edofre/fullcalendar";s:55:"C:\Web\Reclassering\vendor/edofre/yii2-fullcalendar/src";}}s:31:"kartik-v/yii2-widget-timepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-timepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/time";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-timepicker/src";}}s:34:"miloschuman/yii2-highcharts-widget";a:3:{s:4:"name";s:34:"miloschuman/yii2-highcharts-widget";s:7:"version";s:8:"11.0.0.0";s:5:"alias";a:1:{s:23:"@miloschuman/highcharts";s:65:"C:\Web\Reclassering\vendor/miloschuman/yii2-highcharts-widget/src";}}}}";s:3:"log";s:24872:"a:1:{s:8:"messages";a:49:{i:0;a:6:{i:0;s:55:"Bootstrap with kartik\validators\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.006541;i:4;a:0:{}i:5;i:3014040;}i:1;a:6:{i:0;s:59:"Bootstrap with yii2ajaxcrud\ajaxcrud\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.007177;i:4;a:0:{}i:5;i:3119232;}i:2;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.007184;i:4;a:0:{}i:5;i:3119528;}i:3;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.007507;i:4;a:0:{}i:5;i:3148888;}i:4;a:6:{i:0;s:57:"Bootstrap with bedezign\yii2\audit\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.00777;i:4;a:0:{}i:5;i:3177000;}i:5;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.008656;i:4;a:0:{}i:5;i:3397568;}i:6;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.008664;i:4;a:0:{}i:5;i:3398208;}i:7;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.013815;i:4;a:0:{}i:5;i:4298048;}i:8;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.023794;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5687680;}i:9;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.023824;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5689960;}i:14;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.027777;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5747776;}i:17;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.028923;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5768496;}i:20;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.036778;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6172776;}i:23;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.051108;i:4;a:0:{}i:5;i:6875264;}i:24;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.053643;i:4;a:0:{}i:5;i:6989824;}i:25;a:6:{i:0;s:21:"Loading module: audit";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.053677;i:4;a:0:{}i:5;i:6990464;}i:26;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.055794;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7060384;}i:29;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.058006;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7072016;}i:32;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.058732;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7071800;}i:35;a:6:{i:0;s:40:"Bootstrap with bedezign\yii2\audit\Audit";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.07303;i:4;a:0:{}i:5;i:7317728;}i:37;a:6:{i:0;s:31:"Route requested: 'gedetineerde'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:**********.07316;i:4;a:0:{}i:5;i:7316656;}i:38;a:6:{i:0;s:32:"Route to run: gedetineerde/index";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.07616;i:4;a:0:{}i:5;i:7410544;}i:39;a:6:{i:0;s:164:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('gedetineerde/index', '1', '::1', 0, 'GET', '2025-09-30 16:54:00')";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.086243;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7682392;}i:42;a:6:{i:0;s:72:"SELECT * FROM `notification_trigger` WHERE `route`='/gedetineerde/index'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.094885;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7745904;}i:45;a:6:{i:0;s:73:"Running action: backend\controllers\GedetineerdeController::actionIndex()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:**********.095794;i:4;a:0:{}i:5;i:7740128;}i:46;a:6:{i:0;s:37:"SHOW FULL COLUMNS FROM `gedetineerde`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.100269;i:4;a:2:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\common\models\search\GedetineerdeSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\GedetineerdeController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:39:"common\models\search\GedetineerdeSearch";s:4:"type";s:2:"->";}}i:5;i:7959088;}i:49;a:6:{i:0;s:32:"SHOW CREATE TABLE `gedetineerde`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.10345;i:4;a:2:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\common\models\search\GedetineerdeSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\GedetineerdeController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:39:"common\models\search\GedetineerdeSearch";s:4:"type";s:2:"->";}}i:5;i:7974736;}i:52;a:6:{i:0;s:780:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'gedetineerde' AND `kcu`.`TABLE_NAME` = 'gedetineerde'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.105456;i:4;a:2:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\common\models\search\GedetineerdeSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\GedetineerdeController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:39:"common\models\search\GedetineerdeSearch";s:4:"type";s:2:"->";}}i:5;i:7977904;}i:55;a:6:{i:0;s:77:"Rendering view file: C:\Web\Reclassering\backend\views\gedetineerde\index.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.111006;i:4;a:1:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\GedetineerdeController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:8433728;}i:56;a:6:{i:0;s:24:"Loading module: gridview";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.15246;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-krajee-base\src\Config.php";s:4:"line";i:313;s:8:"function";s:9:"getModule";s:5:"class";s:15:"yii\base\Module";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1255;s:8:"function";s:9:"getModule";s:5:"class";s:18:"kartik\base\Config";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:824;s:8:"function";s:10:"initModule";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:9818792;}i:57;a:6:{i:0;s:35:"SELECT COUNT(*) FROM `gedetineerde`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.177778;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10585496;}i:60;a:6:{i:0;s:37:"SELECT * FROM `gedetineerde` LIMIT 20";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.201555;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:56:"C:\Web\Reclassering\backend\views\gedetineerde\index.php";s:4:"line";i:22;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11186048;}i:63;a:6:{i:0;s:71:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\main.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.210726;i:4;a:1:{i:0;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\GedetineerdeController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11440192;}i:64;a:6:{i:0;s:73:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\navbar.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.222563;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\GedetineerdeController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11550104;}i:65;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.225131;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11630456;}i:68;a:6:{i:0;s:107:"SELECT * FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0) ORDER BY `created_at` DESC LIMIT 10";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.227162;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11645960;}i:71;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.228348;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11655480;}i:74;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.230749;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11667496;}i:77;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.231898;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11670400;}i:80;a:6:{i:0;s:79:"Rendering view file: C:\Web\Reclassering\common\widgets\views\notifications.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.235194;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:28;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11678400;}i:81;a:6:{i:0;s:73:"Rendering view file: C:\Web\Reclassering\common\widgets\views\profile.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.23594;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\ProfileWidget.php";s:4:"line";i:32;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:26;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11687960;}i:82;a:6:{i:0;s:74:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\sidebar.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.2362;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:49;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\GedetineerdeController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11653248;}i:83;a:6:{i:0;s:73:"Rendering view file: C:\Web\Reclassering\common\widgets\views\sidebar.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.236479;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:49;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11699280;}i:84;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.23843;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11777432;}i:87;a:6:{i:0;s:74:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\content.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.242217;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:52;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\GedetineerdeController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11812328;}i:88;a:6:{i:0;s:82:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\control-sidebar.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.244664;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:56;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\GedetineerdeController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11938032;}i:89;a:6:{i:0;s:73:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\footer.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.245443;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\GedetineerdeController.php";s:4:"line";i:46;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11956736;}i:90;a:6:{i:0;s:72:"SELECT * FROM `notification_trigger` WHERE `route`='/gedetineerde/index'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.250252;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:44;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:11950208;}i:93;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.25655007362366, `memory_max`=12161096 WHERE `id`=5626";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.251488;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:11952136;}}}";s:9:"profiling";s:31916:"a:3:{s:6:"memory";i:12161096;s:4:"time";d:0.26304101943969727;s:8:"messages";a:46:{i:10;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.023831;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5690768;}i:11;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.02585;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5734072;}i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.025896;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5733856;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.027719;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5746488;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.02779;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5748688;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.028394;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5751264;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.028941;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5769536;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.030509;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5772064;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.03686;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6173160;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.037706;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6175528;}i:27;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.055861;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7061296;}i:28;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.057956;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7070728;}i:30;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.058019;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7072928;}i:31;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.058641;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7074840;}i:33;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.058746;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7073480;}i:34;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.060232;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7075336;}i:40;a:6:{i:0;s:164:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('gedetineerde/index', '1', '::1', 0, 'GET', '2025-09-30 16:54:00')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.086301;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7683752;}i:41;a:6:{i:0;s:164:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('gedetineerde/index', '1', '::1', 0, 'GET', '2025-09-30 16:54:00')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.091631;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7685544;}i:43;a:6:{i:0;s:72:"SELECT * FROM `notification_trigger` WHERE `route`='/gedetineerde/index'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.094943;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7747408;}i:44;a:6:{i:0;s:72:"SELECT * FROM `notification_trigger` WHERE `route`='/gedetineerde/index'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.095727;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7748888;}i:47;a:6:{i:0;s:37:"SHOW FULL COLUMNS FROM `gedetineerde`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.100323;i:4;a:2:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\common\models\search\GedetineerdeSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\GedetineerdeController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:39:"common\models\search\GedetineerdeSearch";s:4:"type";s:2:"->";}}i:5;i:7960376;}i:48;a:6:{i:0;s:37:"SHOW FULL COLUMNS FROM `gedetineerde`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.103364;i:4;a:2:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\common\models\search\GedetineerdeSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\GedetineerdeController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:39:"common\models\search\GedetineerdeSearch";s:4:"type";s:2:"->";}}i:5;i:7973064;}i:50;a:6:{i:0;s:32:"SHOW CREATE TABLE `gedetineerde`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.103477;i:4;a:2:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\common\models\search\GedetineerdeSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\GedetineerdeController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:39:"common\models\search\GedetineerdeSearch";s:4:"type";s:2:"->";}}i:5;i:7976024;}i:51;a:6:{i:0;s:32:"SHOW CREATE TABLE `gedetineerde`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.104554;i:4;a:2:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\common\models\search\GedetineerdeSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\GedetineerdeController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:39:"common\models\search\GedetineerdeSearch";s:4:"type";s:2:"->";}}i:5;i:7978696;}i:53;a:6:{i:0;s:780:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'gedetineerde' AND `kcu`.`TABLE_NAME` = 'gedetineerde'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.105507;i:4;a:2:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\common\models\search\GedetineerdeSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\GedetineerdeController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:39:"common\models\search\GedetineerdeSearch";s:4:"type";s:2:"->";}}i:5;i:7979320;}i:54;a:6:{i:0;s:780:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'gedetineerde' AND `kcu`.`TABLE_NAME` = 'gedetineerde'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.108129;i:4;a:2:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\common\models\search\GedetineerdeSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\GedetineerdeController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:39:"common\models\search\GedetineerdeSearch";s:4:"type";s:2:"->";}}i:5;i:7982768;}i:58;a:6:{i:0;s:35:"SELECT COUNT(*) FROM `gedetineerde`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.177854;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10587160;}i:59;a:6:{i:0;s:35:"SELECT COUNT(*) FROM `gedetineerde`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.178751;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10588784;}i:61;a:6:{i:0;s:37:"SELECT * FROM `gedetineerde` LIMIT 20";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.201615;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:56:"C:\Web\Reclassering\backend\views\gedetineerde\index.php";s:4:"line";i:22;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11187712;}i:62;a:6:{i:0;s:37:"SELECT * FROM `gedetineerde` LIMIT 20";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.202601;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:56:"C:\Web\Reclassering\backend\views\gedetineerde\index.php";s:4:"line";i:22;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11200072;}i:66;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.225215;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11632232;}i:67;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.22628;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11633928;}i:69;a:6:{i:0;s:107:"SELECT * FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0) ORDER BY `created_at` DESC LIMIT 10";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.227192;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11647736;}i:70;a:6:{i:0;s:107:"SELECT * FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0) ORDER BY `created_at` DESC LIMIT 10";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.228246;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11651376;}i:72;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.22837;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11656504;}i:73;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.230608;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11664808;}i:75;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.230823;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11669160;}i:76;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.231723;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11671960;}i:78;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.23192;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11671552;}i:79;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.234544;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11675400;}i:85;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.238497;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11779104;}i:86;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.240004;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11811712;}i:91;a:6:{i:0;s:72:"SELECT * FROM `notification_trigger` WHERE `route`='/gedetineerde/index'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.250307;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:44;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:11951392;}i:92;a:6:{i:0;s:72:"SELECT * FROM `notification_trigger` WHERE `route`='/gedetineerde/index'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.251174;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:44;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:11952872;}i:94;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.25655007362366, `memory_max`=12161096 WHERE `id`=5626";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.251557;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:11953480;}i:95;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.25655007362366, `memory_max`=12161096 WHERE `id`=5626";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.25575;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:11954880;}}}";s:2:"db";s:31146:"a:1:{s:8:"messages";a:44:{i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.025896;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5733856;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.027719;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5746488;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.02779;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5748688;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.028394;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5751264;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.028941;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5769536;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.030509;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5772064;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.03686;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6173160;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.037706;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6175528;}i:27;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.055861;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7061296;}i:28;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.057956;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7070728;}i:30;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.058019;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7072928;}i:31;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.058641;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7074840;}i:33;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.058746;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7073480;}i:34;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.060232;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7075336;}i:40;a:6:{i:0;s:164:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('gedetineerde/index', '1', '::1', 0, 'GET', '2025-09-30 16:54:00')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.086301;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7683752;}i:41;a:6:{i:0;s:164:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('gedetineerde/index', '1', '::1', 0, 'GET', '2025-09-30 16:54:00')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.091631;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7685544;}i:43;a:6:{i:0;s:72:"SELECT * FROM `notification_trigger` WHERE `route`='/gedetineerde/index'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.094943;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7747408;}i:44;a:6:{i:0;s:72:"SELECT * FROM `notification_trigger` WHERE `route`='/gedetineerde/index'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.095727;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7748888;}i:47;a:6:{i:0;s:37:"SHOW FULL COLUMNS FROM `gedetineerde`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.100323;i:4;a:2:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\common\models\search\GedetineerdeSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\GedetineerdeController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:39:"common\models\search\GedetineerdeSearch";s:4:"type";s:2:"->";}}i:5;i:7960376;}i:48;a:6:{i:0;s:37:"SHOW FULL COLUMNS FROM `gedetineerde`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.103364;i:4;a:2:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\common\models\search\GedetineerdeSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\GedetineerdeController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:39:"common\models\search\GedetineerdeSearch";s:4:"type";s:2:"->";}}i:5;i:7973064;}i:50;a:6:{i:0;s:32:"SHOW CREATE TABLE `gedetineerde`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.103477;i:4;a:2:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\common\models\search\GedetineerdeSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\GedetineerdeController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:39:"common\models\search\GedetineerdeSearch";s:4:"type";s:2:"->";}}i:5;i:7976024;}i:51;a:6:{i:0;s:32:"SHOW CREATE TABLE `gedetineerde`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.104554;i:4;a:2:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\common\models\search\GedetineerdeSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\GedetineerdeController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:39:"common\models\search\GedetineerdeSearch";s:4:"type";s:2:"->";}}i:5;i:7978696;}i:53;a:6:{i:0;s:780:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'gedetineerde' AND `kcu`.`TABLE_NAME` = 'gedetineerde'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.105507;i:4;a:2:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\common\models\search\GedetineerdeSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\GedetineerdeController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:39:"common\models\search\GedetineerdeSearch";s:4:"type";s:2:"->";}}i:5;i:7979320;}i:54;a:6:{i:0;s:780:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'gedetineerde' AND `kcu`.`TABLE_NAME` = 'gedetineerde'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.108129;i:4;a:2:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\common\models\search\GedetineerdeSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:66:"C:\Web\Reclassering\backend\controllers\GedetineerdeController.php";s:4:"line";i:44;s:8:"function";s:6:"search";s:5:"class";s:39:"common\models\search\GedetineerdeSearch";s:4:"type";s:2:"->";}}i:5;i:7982768;}i:58;a:6:{i:0;s:35:"SELECT COUNT(*) FROM `gedetineerde`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.177854;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10587160;}i:59;a:6:{i:0;s:35:"SELECT COUNT(*) FROM `gedetineerde`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.178751;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10588784;}i:61;a:6:{i:0;s:37:"SELECT * FROM `gedetineerde` LIMIT 20";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.201615;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:56:"C:\Web\Reclassering\backend\views\gedetineerde\index.php";s:4:"line";i:22;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11187712;}i:62;a:6:{i:0;s:37:"SELECT * FROM `gedetineerde` LIMIT 20";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.202601;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:56:"C:\Web\Reclassering\backend\views\gedetineerde\index.php";s:4:"line";i:22;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11200072;}i:66;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.225215;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11632232;}i:67;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.22628;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11633928;}i:69;a:6:{i:0;s:107:"SELECT * FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0) ORDER BY `created_at` DESC LIMIT 10";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.227192;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11647736;}i:70;a:6:{i:0;s:107:"SELECT * FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0) ORDER BY `created_at` DESC LIMIT 10";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.228246;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11651376;}i:72;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.22837;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11656504;}i:73;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.230608;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11664808;}i:75;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.230823;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11669160;}i:76;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.231723;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11671960;}i:78;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.23192;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11671552;}i:79;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.234544;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11675400;}i:85;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.238497;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11779104;}i:86;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.240004;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11811712;}i:91;a:6:{i:0;s:72:"SELECT * FROM `notification_trigger` WHERE `route`='/gedetineerde/index'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.250307;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:44;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:11951392;}i:92;a:6:{i:0;s:72:"SELECT * FROM `notification_trigger` WHERE `route`='/gedetineerde/index'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.251174;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:44;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:11952872;}i:94;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.25655007362366, `memory_max`=12161096 WHERE `id`=5626";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.251557;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:11953480;}i:95;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.25655007362366, `memory_max`=12161096 WHERE `id`=5626";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.25575;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:11954880;}}}";s:5:"event";s:32510:"a:174:{i:0;a:5:{s:4:"time";d:**********.021485;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:**********.025842;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:**********.038083;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:3;a:5:{s:4:"time";d:**********.038116;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:4;a:5:{s:4:"time";d:**********.044944;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:5;a:5:{s:4:"time";d:**********.07311;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:6;a:5:{s:4:"time";d:**********.080182;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:7;a:5:{s:4:"time";d:**********.080246;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:8;a:5:{s:4:"time";d:**********.08477;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:9;a:5:{s:4:"time";d:**********.092178;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:10;a:5:{s:4:"time";d:**********.092204;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:11;a:5:{s:4:"time";d:**********.092696;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:42:"backend\controllers\GedetineerdeController";}i:12;a:5:{s:4:"time";d:**********.094651;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:13;a:5:{s:4:"time";d:**********.096821;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"common\models\search\GedetineerdeSearch";}i:14;a:5:{s:4:"time";d:**********.096855;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:15;a:5:{s:4:"time";d:**********.0985;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"common\models\search\GedetineerdeSearch";}i:16;a:5:{s:4:"time";d:**********.108224;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"common\models\search\GedetineerdeSearch";}i:17;a:5:{s:4:"time";d:**********.110986;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:18;a:5:{s:4:"time";d:**********.148543;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"yii2ajaxcrud\ajaxcrud\BulkButtonWidget";}i:19;a:5:{s:4:"time";d:**********.14902;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"yii2ajaxcrud\ajaxcrud\BulkButtonWidget";}i:20;a:5:{s:4:"time";d:**********.149046;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"yii2ajaxcrud\ajaxcrud\BulkButtonWidget";}i:21;a:5:{s:4:"time";d:**********.155542;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:22;a:5:{s:4:"time";d:**********.1556;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:23;a:5:{s:4:"time";d:**********.155642;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:24;a:5:{s:4:"time";d:**********.155748;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\grid\GridView";}i:25;a:5:{s:4:"time";d:**********.175647;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:26;a:5:{s:4:"time";d:**********.175779;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:27;a:5:{s:4:"time";d:**********.17582;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:28;a:5:{s:4:"time";d:**********.175852;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:29;a:5:{s:4:"time";d:**********.175908;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\grid\GridView";}i:30;a:5:{s:4:"time";d:**********.178821;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:31;a:5:{s:4:"time";d:**********.178851;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:32;a:5:{s:4:"time";d:**********.178862;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:33;a:5:{s:4:"time";d:**********.178871;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:34;a:5:{s:4:"time";d:**********.178879;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:35;a:5:{s:4:"time";d:**********.178901;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:36;a:5:{s:4:"time";d:**********.178909;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:37;a:5:{s:4:"time";d:**********.178921;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:38;a:5:{s:4:"time";d:**********.178929;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:39;a:5:{s:4:"time";d:**********.178937;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:40;a:5:{s:4:"time";d:**********.178944;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:41;a:5:{s:4:"time";d:**********.178953;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:42;a:5:{s:4:"time";d:**********.178962;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:43;a:5:{s:4:"time";d:**********.17897;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:44;a:5:{s:4:"time";d:**********.178979;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:45;a:5:{s:4:"time";d:**********.178986;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:46;a:5:{s:4:"time";d:**********.178994;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:47;a:5:{s:4:"time";d:**********.179011;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:48;a:5:{s:4:"time";d:**********.179021;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:49;a:5:{s:4:"time";d:**********.179029;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:50;a:5:{s:4:"time";d:**********.179037;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:51;a:5:{s:4:"time";d:**********.179044;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:52;a:5:{s:4:"time";d:**********.179053;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:53;a:5:{s:4:"time";d:**********.179063;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:54;a:5:{s:4:"time";d:**********.17907;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:55;a:5:{s:4:"time";d:**********.179078;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:56;a:5:{s:4:"time";d:**********.179087;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:57;a:5:{s:4:"time";d:**********.179095;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:58;a:5:{s:4:"time";d:**********.179103;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:59;a:5:{s:4:"time";d:**********.179111;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:60;a:5:{s:4:"time";d:**********.179119;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:61;a:5:{s:4:"time";d:**********.179127;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:62;a:5:{s:4:"time";d:**********.179135;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:63;a:5:{s:4:"time";d:**********.179143;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:64;a:5:{s:4:"time";d:**********.179151;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:65;a:5:{s:4:"time";d:**********.17916;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:66;a:5:{s:4:"time";d:**********.179167;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:67;a:5:{s:4:"time";d:**********.179175;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:68;a:5:{s:4:"time";d:**********.179184;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:69;a:5:{s:4:"time";d:**********.179191;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:70;a:5:{s:4:"time";d:**********.183176;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"kartik\bs5dropdown\ButtonDropdown";}i:71;a:5:{s:4:"time";d:**********.183215;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"kartik\bs5dropdown\ButtonDropdown";}i:72;a:5:{s:4:"time";d:**********.185794;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\bootstrap5\Button";}i:73;a:5:{s:4:"time";d:**********.185823;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\bootstrap5\Button";}i:74;a:5:{s:4:"time";d:**********.18588;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\bootstrap5\Button";}i:75;a:5:{s:4:"time";d:**********.187541;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"kartik\bs5dropdown\Dropdown";}i:76;a:5:{s:4:"time";d:**********.187569;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"kartik\bs5dropdown\Dropdown";}i:77;a:5:{s:4:"time";d:**********.1896;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"kartik\bs5dropdown\Dropdown";}i:78;a:5:{s:4:"time";d:**********.189676;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"kartik\bs5dropdown\ButtonDropdown";}i:79;a:5:{s:4:"time";d:**********.192802;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\dialog\Dialog";}i:80;a:5:{s:4:"time";d:**********.192849;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\dialog\Dialog";}i:81;a:5:{s:4:"time";d:**********.193935;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:82;a:5:{s:4:"time";d:**********.19398;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:83;a:5:{s:4:"time";d:**********.194009;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:84;a:5:{s:4:"time";d:**********.194046;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:85;a:5:{s:4:"time";d:**********.195861;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\dialog\Dialog";}i:86;a:5:{s:4:"time";d:**********.199447;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:87;a:5:{s:4:"time";d:**********.201357;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Gedetineerde";}i:88;a:5:{s:4:"time";d:**********.202638;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Gedetineerde";}i:89;a:5:{s:4:"time";d:**********.202669;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Gedetineerde";}i:90;a:5:{s:4:"time";d:**********.202684;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Gedetineerde";}i:91;a:5:{s:4:"time";d:**********.202694;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Gedetineerde";}i:92;a:5:{s:4:"time";d:**********.202704;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Gedetineerde";}i:93;a:5:{s:4:"time";d:**********.202713;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Gedetineerde";}i:94;a:5:{s:4:"time";d:**********.202722;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Gedetineerde";}i:95;a:5:{s:4:"time";d:**********.20273;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Gedetineerde";}i:96;a:5:{s:4:"time";d:**********.202739;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Gedetineerde";}i:97;a:5:{s:4:"time";d:**********.202748;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Gedetineerde";}i:98;a:5:{s:4:"time";d:**********.202756;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Gedetineerde";}i:99;a:5:{s:4:"time";d:**********.202758;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Gedetineerde";}i:100;a:5:{s:4:"time";d:**********.20276;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Gedetineerde";}i:101;a:5:{s:4:"time";d:**********.202762;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Gedetineerde";}i:102;a:5:{s:4:"time";d:**********.202764;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Gedetineerde";}i:103;a:5:{s:4:"time";d:**********.202766;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Gedetineerde";}i:104;a:5:{s:4:"time";d:**********.202768;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Gedetineerde";}i:105;a:5:{s:4:"time";d:**********.20277;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Gedetineerde";}i:106;a:5:{s:4:"time";d:**********.202772;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Gedetineerde";}i:107;a:5:{s:4:"time";d:**********.202774;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Gedetineerde";}i:108;a:5:{s:4:"time";d:**********.202803;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:109;a:5:{s:4:"time";d:**********.206105;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\widgets\LinkPager";}i:110;a:5:{s:4:"time";d:**********.206131;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\widgets\LinkPager";}i:111;a:5:{s:4:"time";d:**********.206156;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\widgets\LinkPager";}i:112;a:5:{s:4:"time";d:**********.206264;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:113;a:5:{s:4:"time";d:**********.207615;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:114;a:5:{s:4:"time";d:**********.207643;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\grid\GridView";}i:115;a:5:{s:4:"time";d:**********.209041;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"yii\bootstrap5\Modal";}i:116;a:5:{s:4:"time";d:**********.210209;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"yii\bootstrap5\Modal";}i:117;a:5:{s:4:"time";d:**********.210313;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"yii\bootstrap5\Modal";}i:118;a:5:{s:4:"time";d:**********.210373;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:119;a:5:{s:4:"time";d:**********.210714;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:120;a:5:{s:4:"time";d:**********.222078;s:4:"name";s:9:"beginPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:121;a:5:{s:4:"time";d:**********.222298;s:4:"name";s:9:"beginBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:122;a:5:{s:4:"time";d:**********.222549;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:123;a:5:{s:4:"time";d:**********.223891;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"common\widgets\NotificationWidget";}i:124;a:5:{s:4:"time";d:**********.223916;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"common\widgets\NotificationWidget";}i:125;a:5:{s:4:"time";d:**********.224864;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:126;a:5:{s:4:"time";d:**********.226342;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:127;a:5:{s:4:"time";d:**********.228294;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:128;a:5:{s:4:"time";d:**********.234682;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:129;a:5:{s:4:"time";d:**********.234739;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:130;a:5:{s:4:"time";d:**********.234819;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:131;a:5:{s:4:"time";d:**********.234833;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:132;a:5:{s:4:"time";d:**********.234847;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:133;a:5:{s:4:"time";d:**********.235186;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:134;a:5:{s:4:"time";d:**********.235596;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:135;a:5:{s:4:"time";d:**********.235609;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"common\widgets\NotificationWidget";}i:136;a:5:{s:4:"time";d:**********.235855;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\ProfileWidget";}i:137;a:5:{s:4:"time";d:**********.235862;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\ProfileWidget";}i:138;a:5:{s:4:"time";d:**********.235936;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:139;a:5:{s:4:"time";d:**********.236121;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:140;a:5:{s:4:"time";d:**********.236128;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\ProfileWidget";}i:141;a:5:{s:4:"time";d:**********.236145;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:142;a:5:{s:4:"time";d:**********.236198;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:143;a:5:{s:4:"time";d:**********.236417;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\SidebarWidget";}i:144;a:5:{s:4:"time";d:**********.236422;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\SidebarWidget";}i:145;a:5:{s:4:"time";d:**********.236476;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:146;a:5:{s:4:"time";d:**********.237479;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:147;a:5:{s:4:"time";d:**********.241688;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"common\components\LucideSidebarMenuHelper";}i:148;a:5:{s:4:"time";d:**********.241704;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"common\components\LucideSidebarMenuHelper";}i:149;a:5:{s:4:"time";d:**********.242079;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"common\components\LucideSidebarMenuHelper";}i:150;a:5:{s:4:"time";d:**********.242107;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:151;a:5:{s:4:"time";d:**********.242114;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\SidebarWidget";}i:152;a:5:{s:4:"time";d:**********.242123;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:153;a:5:{s:4:"time";d:**********.242213;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:154;a:5:{s:4:"time";d:**********.243983;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"yii\bootstrap5\Breadcrumbs";}i:155;a:5:{s:4:"time";d:**********.244018;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"yii\bootstrap5\Breadcrumbs";}i:156;a:5:{s:4:"time";d:**********.244199;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"yii\bootstrap5\Breadcrumbs";}i:157;a:5:{s:4:"time";d:**********.244304;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:158;a:5:{s:4:"time";d:**********.244652;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:159;a:5:{s:4:"time";d:**********.245122;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:160;a:5:{s:4:"time";d:**********.245429;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:161;a:5:{s:4:"time";d:**********.24592;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:162;a:5:{s:4:"time";d:**********.247096;s:4:"name";s:7:"endBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:163;a:5:{s:4:"time";d:**********.248547;s:4:"name";s:7:"endPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:164;a:5:{s:4:"time";d:**********.249934;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:165;a:5:{s:4:"time";d:**********.24997;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:42:"backend\controllers\GedetineerdeController";}i:166;a:5:{s:4:"time";d:**********.250047;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:167;a:5:{s:4:"time";d:**********.25121;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:168;a:5:{s:4:"time";d:**********.251266;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:169;a:5:{s:4:"time";d:**********.255803;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:170;a:5:{s:4:"time";d:**********.255814;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:171;a:5:{s:4:"time";d:**********.255824;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:172;a:5:{s:4:"time";d:**********.256305;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:173;a:5:{s:4:"time";d:**********.256648;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:92:"a:3:{s:5:"start";d:1759247639.994681;s:3:"end";d:**********.258324;s:6:"memory";i:12161096;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:322:"a:3:{s:8:"messages";a:1:{i:36;a:6:{i:0;s:56:"Pretty URL not enabled. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.073134;i:4;a:0:{}i:5;i:7316760;}}s:5:"route";s:18:"gedetineerde/index";s:6:"action";s:57:"backend\controllers\GedetineerdeController::actionIndex()";}";s:7:"request";s:10437:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:20:{s:12:"content-type";s:0:"";s:14:"content-length";s:1:"0";s:14:"sec-fetch-dest";s:8:"document";s:14:"sec-fetch-user";s:2:"?1";s:14:"sec-fetch-mode";s:8:"navigate";s:14:"sec-fetch-site";s:11:"same-origin";s:25:"upgrade-insecure-requests";s:1:"1";s:18:"sec-ch-ua-platform";s:9:""Windows"";s:16:"sec-ch-ua-mobile";s:2:"?0";s:9:"sec-ch-ua";s:65:""Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36";s:7:"referer";s:33:"http://localhost:8005/backoffice/";s:4:"host";s:14:"localhost:8005";s:6:"cookie";s:730:"sidebar-collapse=false; _csrf-frontend=966348498e53328f1da03e1211c89fe27a4ed76fa2468d0a2ca018176ff962a0a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22C4u1IT1V5ezGiIAFS_5ZjO4m3haIvK7s%22%3B%7D; advanced-frontend-fmz=5buup5va13i3275juhh37tnbie; advanced-backend-fmz=ulp03tq95ncfr014iko0c4f7tr; _identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; _csrf-backend=89617a426ea51cc1cf6127e111843c088c6d8bcde0fa0c7a73770f100a453778a%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22n4wiLOTlXkuHsaYEBHJkvoSxhdjEFdOV%22%3B%7D";s:15:"accept-language";s:14:"en-US,en;q=0.9";s:15:"accept-encoding";s:23:"gzip, deflate, br, zstd";s:6:"accept";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:6:"pragma";s:8:"no-cache";s:10:"connection";s:10:"keep-alive";s:13:"cache-control";s:8:"no-cache";}s:15:"responseHeaders";a:9:{s:12:"X-Powered-By";s:9:"PHP/8.3.3";s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"68dbfd180cc9f";s:16:"X-Debug-Duration";s:3:"263";s:12:"X-Debug-Link";s:64:"/backoffice/index.php?r=debug%2Fdefault%2Fview&tag=68dbfd180cc9f";s:10:"Set-Cookie";s:311:"_identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; expires=Thu, 30 Oct 2025 15:54:00 GMT; Max-Age=2592000; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:18:"gedetineerde/index";s:6:"action";s:57:"backend\controllers\GedetineerdeController::actionIndex()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:106:{s:13:"_FCGI_X_PIPE_";s:53:"\\.\pipe\IISFCGI-425e8255-4685-467e-aa56-94df1521bbaa";s:5:"PHPRC";s:26:"C:\Program Files\PHP\v8.3\";s:21:"PHP_FCGI_MAX_REQUESTS";s:5:"10000";s:15:"ALLUSERSPROFILE";s:14:"C:\ProgramData";s:7:"APPDATA";s:56:"C:\WINDOWS\system32\config\systemprofile\AppData\Roaming";s:15:"APP_POOL_CONFIG";s:57:"C:\inetpub\temp\apppools\Reclassering\Reclassering.config";s:11:"APP_POOL_ID";s:12:"Reclassering";s:17:"ChocolateyInstall";s:25:"C:\ProgramData\chocolatey";s:18:"CommonProgramFiles";s:29:"C:\Program Files\Common Files";s:23:"CommonProgramFiles(x86)";s:35:"C:\Program Files (x86)\Common Files";s:18:"CommonProgramW6432";s:29:"C:\Program Files\Common Files";s:12:"COMPUTERNAME";s:10:"EGOV-L-046";s:7:"ComSpec";s:27:"C:\WINDOWS\system32\cmd.exe";s:10:"DriverData";s:38:"C:\Windows\System32\Drivers\DriverData";s:10:"IGCCSVC_DB";s:416:"AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg==";s:12:"LOCALAPPDATA";s:54:"C:\WINDOWS\system32\config\systemprofile\AppData\Local";s:20:"NUMBER_OF_PROCESSORS";s:2:"12";s:14:"OnlineServices";s:15:"Online Services";s:2:"OS";s:10:"Windows_NT";s:4:"Path";s:582:"C:\Python313\Scripts\;C:\Python313\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\PHP\v8.3;C:\ProgramData\ComposerSetup\bin;C:\ProgramData\ComposerSetup\bin\composer.bat;C:\Program Files (x86)\HP\HP OCR\DB_Lib\;C:\Program Files\HP\Common\HPDestPlgIn\;C:\Program Files (x86)\HP\Common\HPDestPlgIn\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\GitHub CLI\;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:12:"platformcode";s:2:"AN";s:22:"PROCESSOR_ARCHITECTURE";s:5:"AMD64";s:20:"PROCESSOR_IDENTIFIER";s:51:"Intel64 Family 6 Model 186 Stepping 3, GenuineIntel";s:15:"PROCESSOR_LEVEL";s:1:"6";s:18:"PROCESSOR_REVISION";s:4:"ba03";s:11:"ProgramData";s:14:"C:\ProgramData";s:12:"ProgramFiles";s:16:"C:\Program Files";s:17:"ProgramFiles(x86)";s:22:"C:\Program Files (x86)";s:12:"ProgramW6432";s:16:"C:\Program Files";s:12:"PSModulePath";s:93:"C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules";s:6:"PUBLIC";s:15:"C:\Users\<USER>\WINDOWS";s:4:"TEMP";s:15:"C:\WINDOWS\TEMP";s:3:"TMP";s:15:"C:\WINDOWS\TEMP";s:10:"USERDOMAIN";s:3:"GOV";s:8:"USERNAME";s:11:"EGOV-L-046$";s:11:"USERPROFILE";s:40:"C:\WINDOWS\system32\config\systemprofile";s:6:"windir";s:10:"C:\WINDOWS";s:17:"ZES_ENABLE_SYSMAN";s:1:"1";s:14:"ORIG_PATH_INFO";s:21:"/backoffice/index.php";s:3:"URL";s:21:"/backoffice/index.php";s:15:"SERVER_SOFTWARE";s:18:"Microsoft-IIS/10.0";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:18:"SERVER_PORT_SECURE";s:1:"0";s:11:"SERVER_PORT";s:4:"8005";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SCRIPT_NAME";s:21:"/backoffice/index.php";s:15:"SCRIPT_FILENAME";s:41:"C:\Web\Reclassering\backend\web\index.php";s:11:"REQUEST_URI";s:36:"/backoffice/index.php?r=gedetineerde";s:14:"REQUEST_METHOD";s:3:"GET";s:11:"REMOTE_USER";s:0:"";s:11:"REMOTE_PORT";s:5:"55043";s:11:"REMOTE_HOST";s:3:"::1";s:11:"REMOTE_ADDR";s:3:"::1";s:12:"QUERY_STRING";s:14:"r=gedetineerde";s:15:"PATH_TRANSLATED";s:41:"C:\Web\Reclassering\backend\web\index.php";s:10:"LOGON_USER";s:0:"";s:10:"LOCAL_ADDR";s:3:"::1";s:18:"INSTANCE_META_PATH";s:11:"/LM/W3SVC/2";s:13:"INSTANCE_NAME";s:12:"RECLASSERING";s:11:"INSTANCE_ID";s:1:"2";s:20:"HTTPS_SERVER_SUBJECT";s:0:"";s:19:"HTTPS_SERVER_ISSUER";s:0:"";s:19:"HTTPS_SECRETKEYSIZE";s:0:"";s:13:"HTTPS_KEYSIZE";s:0:"";s:5:"HTTPS";s:3:"off";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:13:"DOCUMENT_ROOT";s:32:"C:\Web\Reclassering\frontend\web";s:12:"CONTENT_TYPE";s:0:"";s:14:"CONTENT_LENGTH";s:1:"0";s:12:"CERT_SUBJECT";s:0:"";s:17:"CERT_SERIALNUMBER";s:0:"";s:11:"CERT_ISSUER";s:0:"";s:10:"CERT_FLAGS";s:0:"";s:11:"CERT_COOKIE";s:0:"";s:9:"AUTH_USER";s:0:"";s:13:"AUTH_PASSWORD";s:0:"";s:9:"AUTH_TYPE";s:0:"";s:18:"APPL_PHYSICAL_PATH";s:32:"C:\Web\Reclassering\backend\web\";s:12:"APPL_MD_PATH";s:27:"/LM/W3SVC/2/ROOT/backoffice";s:20:"IIS_UrlRewriteModule";s:13:"7,1,1993,2351";s:19:"HTTP_SEC_FETCH_DEST";s:8:"document";s:19:"HTTP_SEC_FETCH_USER";s:2:"?1";s:19:"HTTP_SEC_FETCH_MODE";s:8:"navigate";s:19:"HTTP_SEC_FETCH_SITE";s:11:"same-origin";s:30:"HTTP_UPGRADE_INSECURE_REQUESTS";s:1:"1";s:23:"HTTP_SEC_CH_UA_PLATFORM";s:9:""Windows"";s:21:"HTTP_SEC_CH_UA_MOBILE";s:2:"?0";s:14:"HTTP_SEC_CH_UA";s:65:""Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36";s:12:"HTTP_REFERER";s:33:"http://localhost:8005/backoffice/";s:9:"HTTP_HOST";s:14:"localhost:8005";s:11:"HTTP_COOKIE";s:730:"sidebar-collapse=false; _csrf-frontend=966348498e53328f1da03e1211c89fe27a4ed76fa2468d0a2ca018176ff962a0a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22C4u1IT1V5ezGiIAFS_5ZjO4m3haIvK7s%22%3B%7D; advanced-frontend-fmz=5buup5va13i3275juhh37tnbie; advanced-backend-fmz=ulp03tq95ncfr014iko0c4f7tr; _identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; _csrf-backend=89617a426ea51cc1cf6127e111843c088c6d8bcde0fa0c7a73770f100a453778a%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22n4wiLOTlXkuHsaYEBHJkvoSxhdjEFdOV%22%3B%7D";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"en-US,en;q=0.9";s:20:"HTTP_ACCEPT_ENCODING";s:23:"gzip, deflate, br, zstd";s:11:"HTTP_ACCEPT";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:11:"HTTP_PRAGMA";s:8:"no-cache";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:18:"HTTP_CACHE_CONTROL";s:8:"no-cache";s:9:"FCGI_ROLE";s:9:"RESPONDER";s:8:"PHP_SELF";s:21:"/backoffice/index.php";s:18:"REQUEST_TIME_FLOAT";d:1759247639.987666;s:12:"REQUEST_TIME";i:1759247639;}s:3:"GET";a:1:{s:1:"r";s:12:"gedetineerde";}s:4:"POST";a:0:{}s:6:"COOKIE";a:6:{s:16:"sidebar-collapse";s:5:"false";s:14:"_csrf-frontend";s:140:"966348498e53328f1da03e1211c89fe27a4ed76fa2468d0a2ca018176ff962a0a:2:{i:0;s:14:"_csrf-frontend";i:1;s:32:"C4u1IT1V5ezGiIAFS_5ZjO4m3haIvK7s";}";s:21:"advanced-frontend-fmz";s:26:"5buup5va13i3275juhh37tnbie";s:20:"advanced-backend-fmz";s:26:"ulp03tq95ncfr014iko0c4f7tr";s:17:"_identity-backend";s:157:"39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba:2:{i:0;s:17:"_identity-backend";i:1;s:46:"[1,"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW",2592000]";}";s:13:"_csrf-backend";s:139:"89617a426ea51cc1cf6127e111843c088c6d8bcde0fa0c7a73770f100a453778a:2:{i:0;s:13:"_csrf-backend";i:1;s:32:"n4wiLOTlXkuHsaYEBHJkvoSxhdjEFdOV";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:4:{s:7:"__flash";a:0:{}s:11:"__returnUrl";s:33:"http://localhost:8005/backoffice/";s:4:"__id";i:1;s:9:"__authKey";s:32:"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW";}}";s:4:"user";s:1549:"a:5:{s:2:"id";i:1;s:8:"identity";a:12:{s:2:"id";s:1:"1";s:8:"username";s:11:"'SuperUser'";s:8:"auth_key";s:34:"'R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW'";s:13:"password_hash";s:62:"'$2y$13$uUSLwNI3so2bzilzzZdKJ.C1qmfyTdLRRT1XVP8jYO1c38iE6dfxS'";s:20:"password_reset_token";s:4:"null";s:5:"email";s:17:"'<EMAIL>'";s:6:"status";s:2:"10";s:10:"created_at";s:10:"1741788198";s:10:"updated_at";s:10:"1749661338";s:18:"verification_token";s:45:"'oZxacBEp5N7LZAqXc3s1haihOCLK8dLU_1741788198'";s:7:"role_id";s:1:"1";s:12:"access_token";s:66:"'CjYetJZp6PI5vAvKuqm6TDSC_2kYfe_LtOkrjMVtEo4IseTg3J-r-Gp3gMgH-pr7'";}s:10:"attributes";a:12:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:8:"Username";}i:2;a:2:{s:9:"attribute";s:8:"auth_key";s:5:"label";s:8:"Auth Key";}i:3;a:2:{s:9:"attribute";s:13:"password_hash";s:5:"label";s:8:"Password";}i:4;a:2:{s:9:"attribute";s:20:"password_reset_token";s:5:"label";s:20:"Password Reset Token";}i:5;a:2:{s:9:"attribute";s:5:"email";s:5:"label";s:5:"Email";}i:6;a:2:{s:9:"attribute";s:6:"status";s:5:"label";s:6:"Status";}i:7;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:8;a:2:{s:9:"attribute";s:10:"updated_at";s:5:"label";s:10:"Updated At";}i:9;a:2:{s:9:"attribute";s:18:"verification_token";s:5:"label";s:18:"Verification Token";}i:10;a:2:{s:9:"attribute";s:7:"role_id";s:5:"label";s:4:"Role";}i:11;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}}s:13:"rolesProvider";N;s:19:"permissionsProvider";N;}";s:5:"asset";s:14836:"a:27:{s:31:"yii2ajaxcrud\ajaxcrud\CrudAsset";a:9:{s:10:"sourcePath";s:64:"C:\Web\Reclassering\vendor\biladina\yii2-ajaxcrud-bs4\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\a6a94290";s:7:"baseUrl";s:27:"/backoffice/assets/a6a94290";s:7:"depends";a:5:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";i:2;s:35:"yii\bootstrap5\BootstrapPluginAsset";i:3;s:25:"kartik\grid\GridViewAsset";i:4;s:38:"yii2ajaxcrud\ajaxcrud\FontAwesomeAsset";}s:2:"js";a:2:{i:0;s:14:"ModalRemote.js";i:1;s:11:"ajaxcrud.js";}s:3:"css";a:1:{i:0;s:12:"ajaxcrud.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\web\YiiAsset";a:9:{s:10:"sourcePath";s:46:"C:\Web\Reclassering\vendor\yiisoft\yii2/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\ea09678e";s:7:"baseUrl";s:27:"/backoffice/assets/ea09678e";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;s:6:"yii.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:19:"yii\web\JqueryAsset";a:9:{s:10:"sourcePath";s:50:"C:\Web\Reclassering/vendor/bower-asset/jquery/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\8bc86ca8";s:7:"baseUrl";s:27:"/backoffice/assets/8bc86ca8";s:7:"depends";a:0:{}s:2:"js";a:1:{i:0;s:9:"jquery.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:29:"yii\bootstrap5\BootstrapAsset";a:9:{s:10:"sourcePath";s:53:"C:\Web\Reclassering/vendor/bower-asset/bootstrap/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\4ceb4c69";s:7:"baseUrl";s:27:"/backoffice/assets/4ceb4c69";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:17:"css/bootstrap.css";}s:9:"jsOptions";a:1:{s:8:"position";i:1;}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:35:"yii\bootstrap5\BootstrapPluginAsset";a:9:{s:10:"sourcePath";s:53:"C:\Web\Reclassering/vendor/bower-asset/bootstrap/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\4ceb4c69";s:7:"baseUrl";s:27:"/backoffice/assets/4ceb4c69";s:7:"depends";a:1:{i:0;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:22:"js/bootstrap.bundle.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:25:"kartik\grid\GridViewAsset";a:17:{s:10:"sourcePath";s:56:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\a9ee7db7";s:7:"baseUrl";s:27:"/backoffice/assets/a9ee7db7";s:7:"depends";a:4:{i:0;s:25:"kartik\dialog\DialogAsset";i:1;s:22:"yii\grid\GridViewAsset";i:2;s:16:"yii\web\YiiAsset";i:3;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:15:"css/kv-grid.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:25:"kartik\dialog\DialogAsset";a:17:{s:10:"sourcePath";s:58:"C:\Web\Reclassering\vendor\kartik-v\yii2-dialog\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\1f8ca467";s:7:"baseUrl";s:27:"/backoffice/assets/1f8ca467";s:7:"depends";a:1:{i:0;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:12:"js/dialog.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:1:{s:8:"position";i:1;}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:22:"yii\grid\GridViewAsset";a:9:{s:10:"sourcePath";s:46:"C:\Web\Reclassering\vendor\yiisoft\yii2/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\ea09678e";s:7:"baseUrl";s:27:"/backoffice/assets/ea09678e";s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:2:"js";a:1:{i:0;s:15:"yii.gridView.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:38:"yii2ajaxcrud\ajaxcrud\FontAwesomeAsset";a:9:{s:10:"sourcePath";s:51:"C:\Web\Reclassering/vendor/fortawesome/font-awesome";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\713ebcc9";s:7:"baseUrl";s:27:"/backoffice/assets/713ebcc9";s:7:"depends";a:0:{}s:2:"js";a:1:{i:0;s:13:"js/all.min.js";}s:3:"css";a:1:{i:0;s:15:"css/all.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:31:"kartik\grid\CheckboxColumnAsset";a:17:{s:10:"sourcePath";s:56:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\a9ee7db7";s:7:"baseUrl";s:27:"/backoffice/assets/a9ee7db7";s:7:"depends";a:3:{i:0;s:25:"kartik\grid\GridViewAsset";i:1;s:16:"yii\web\YiiAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:22:"js/kv-grid-checkbox.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:32:"kartik\bs5dropdown\DropdownAsset";a:17:{s:10:"sourcePath";s:71:"C:\Web\Reclassering\vendor\kartik-v\yii2-bootstrap5-dropdown\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\14348fa8";s:7:"baseUrl";s:27:"/backoffice/assets/14348fa8";s:7:"depends";a:3:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";i:2;s:35:"yii\bootstrap5\BootstrapPluginAsset";}s:2:"js";a:1:{i:0;s:14:"js/dropdown.js";}s:3:"css";a:1:{i:0;s:16:"css/dropdown.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:1;s:9:"bsVersion";s:3:"5.x";s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:34:"kartik\dialog\DialogBootstrapAsset";a:17:{s:10:"sourcePath";s:58:"C:\Web\Reclassering\vendor\kartik-v\yii2-dialog\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\1f8ca467";s:7:"baseUrl";s:27:"/backoffice/assets/1f8ca467";s:7:"depends";a:4:{i:0;s:25:"kartik\dialog\DialogAsset";i:1;s:16:"yii\web\YiiAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";i:3;s:35:"yii\bootstrap5\BootstrapPluginAsset";}s:2:"js";a:1:{i:0;s:22:"js/bootstrap-dialog.js";}s:3:"css";a:1:{i:0;s:28:"css/bootstrap-dialog-bs4.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:1;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:28:"kartik\dialog\DialogYiiAsset";a:17:{s:10:"sourcePath";s:58:"C:\Web\Reclassering\vendor\kartik-v\yii2-dialog\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\1f8ca467";s:7:"baseUrl";s:27:"/backoffice/assets/1f8ca467";s:7:"depends";a:3:{i:0;s:25:"kartik\dialog\DialogAsset";i:1;s:16:"yii\web\YiiAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:16:"js/dialog-yii.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:27:"kartik\grid\GridExportAsset";a:17:{s:10:"sourcePath";s:56:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\a9ee7db7";s:7:"baseUrl";s:27:"/backoffice/assets/a9ee7db7";s:7:"depends";a:3:{i:0;s:25:"kartik\dialog\DialogAsset";i:1;s:16:"yii\web\YiiAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:20:"js/kv-grid-export.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:34:"kartik\grid\GridResizeColumnsAsset";a:17:{s:10:"sourcePath";s:56:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\a9ee7db7";s:7:"baseUrl";s:27:"/backoffice/assets/a9ee7db7";s:7:"depends";a:3:{i:0;s:16:"yii\web\YiiAsset";i:1;s:25:"kartik\grid\GridViewAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:29:"js/jquery.resizableColumns.js";}s:3:"css";a:1:{i:0;s:31:"css/jquery.resizableColumns.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:21:"yii\widgets\PjaxAsset";a:9:{s:10:"sourcePath";s:48:"C:\Web\Reclassering/vendor/bower-asset/yii2-pjax";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\38002d64";s:7:"baseUrl";s:27:"/backoffice/assets/38002d64";s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:2:"js";a:1:{i:0;s:14:"jquery.pjax.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:41:"hail812\adminlte3\assets\FontAwesomeAsset";a:9:{s:10:"sourcePath";s:74:"C:\Web\Reclassering/vendor/almasaeed2010/adminlte/plugins/fontawesome-free";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\9a5ee6fe";s:7:"baseUrl";s:27:"/backoffice/assets/9a5ee6fe";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:15:"css/all.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:38:"hail812\adminlte3\assets\AdminLteAsset";a:9:{s:10:"sourcePath";s:54:"C:\Web\Reclassering/vendor/almasaeed2010/adminlte/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\f545690b";s:7:"baseUrl";s:27:"/backoffice/assets/f545690b";s:7:"depends";a:2:{i:0;s:34:"hail812\adminlte3\assets\BaseAsset";i:1;s:36:"hail812\adminlte3\assets\PluginAsset";}s:2:"js";a:1:{i:0;s:18:"js/adminlte.min.js";}s:3:"css";a:1:{i:0;s:20:"css/adminlte.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:34:"hail812\adminlte3\assets\BaseAsset";a:9:{s:10:"sourcePath";N;s:8:"basePath";N;s:7:"baseUrl";N;s:7:"depends";a:3:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap4\BootstrapAsset";i:2;s:35:"yii\bootstrap4\BootstrapPluginAsset";}s:2:"js";a:0:{}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:29:"yii\bootstrap4\BootstrapAsset";a:9:{s:10:"sourcePath";s:51:"C:\Web\Reclassering/vendor/npm-asset/bootstrap/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\75eeaf84";s:7:"baseUrl";s:27:"/backoffice/assets/75eeaf84";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:17:"css/bootstrap.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:35:"yii\bootstrap4\BootstrapPluginAsset";a:9:{s:10:"sourcePath";s:51:"C:\Web\Reclassering/vendor/npm-asset/bootstrap/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\75eeaf84";s:7:"baseUrl";s:27:"/backoffice/assets/75eeaf84";s:7:"depends";a:2:{i:0;s:19:"yii\web\JqueryAsset";i:1;s:29:"yii\bootstrap4\BootstrapAsset";}s:2:"js";a:1:{i:0;s:22:"js/bootstrap.bundle.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:36:"hail812\adminlte3\assets\PluginAsset";a:9:{s:10:"sourcePath";s:57:"C:\Web\Reclassering/vendor/almasaeed2010/adminlte/plugins";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\834b4d89";s:7:"baseUrl";s:27:"/backoffice/assets/834b4d89";s:7:"depends";a:1:{i:0;s:34:"hail812\adminlte3\assets\BaseAsset";}s:2:"js";a:0:{}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:46:"/backoffice/assets/e4a6bb80/control_sidebar.js";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:31:"C:\Web\Reclassering\backend\web";s:7:"baseUrl";s:0:"";s:7:"depends";a:1:{i:0;s:39:"\hail812\adminlte3\assets\AdminLteAsset";}s:2:"js";a:1:{i:0;a:1:{i:0;s:45:"backoffice/assets/e4a6bb80/control_sidebar.js";}}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:39:"\hail812\adminlte3\assets\AdminLteAsset";a:9:{s:10:"sourcePath";s:54:"C:\Web\Reclassering/vendor/almasaeed2010/adminlte/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\f545690b";s:7:"baseUrl";s:27:"/backoffice/assets/f545690b";s:7:"depends";a:2:{i:0;s:34:"hail812\adminlte3\assets\BaseAsset";i:1;s:36:"hail812\adminlte3\assets\PluginAsset";}s:2:"js";a:1:{i:0;s:18:"js/adminlte.min.js";}s:3:"css";a:1:{i:0;s:20:"css/adminlte.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:23:"backend\assets\AppAsset";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:31:"C:\Web\Reclassering\backend\web";s:7:"baseUrl";s:11:"/backoffice";s:7:"depends";a:7:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";i:2;s:19:"yii\web\JqueryAsset";i:3;s:16:"yii\jui\JuiAsset";i:4;s:29:"kartik\sortable\SortableAsset";i:5;s:38:"hail812\adminlte3\assets\AdminLteAsset";i:6;s:36:"hail812\adminlte3\assets\PluginAsset";}s:2:"js";a:3:{i:0;s:50:"https://unpkg.com/lucide@latest/dist/umd/lucide.js";i:1;s:10:"js/main.js";i:2;s:75:"https://cdn.jsdelivr.net/npm/sweetalert2@11.7.1/dist/sweetalert2.all.min.js";}s:3:"css";a:1:{i:0;s:12:"css/site.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\jui\JuiAsset";a:9:{s:10:"sourcePath";s:48:"C:\Web\Reclassering/vendor/bower-asset/jquery-ui";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\a78fe255";s:7:"baseUrl";s:27:"/backoffice/assets/a78fe255";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;s:12:"jquery-ui.js";}s:3:"css";a:1:{i:0;s:31:"themes/smoothness/jquery-ui.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:29:"kartik\sortable\SortableAsset";a:17:{s:10:"sourcePath";s:60:"C:\Web\Reclassering\vendor\kartik-v\yii2-sortable\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\3202e5a2";s:7:"baseUrl";s:27:"/backoffice/assets/3202e5a2";s:7:"depends";a:2:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:2:{i:0;s:19:"js/html5sortable.js";i:1;s:23:"js/kv-html5-sortable.js";}s:3:"css";a:1:{i:0;s:25:"css/kv-html5-sortable.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}}";s:7:"summary";a:13:{s:3:"tag";s:13:"68dbfd180cc9f";s:3:"url";s:57:"http://localhost:8005/backoffice/index.php?r=gedetineerde";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:3:"::1";s:4:"time";d:1759247639.987666;s:10:"statusCode";i:200;s:8:"sqlCount";i:22;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:12161096;s:14:"processingTime";d:0.26304101943969727;}s:10:"exceptions";a:0:{}}