<?php

namespace common\models\search;

use common\models\DocumentPermission;
use yii\base\Model;
use yii\data\ActiveDataProvider;

/**
 * DocumentUploadPermissionSearch represents the model behind the search form of `common\models\DocumentUploadPermission`.
 */
class DocumentPermissionSearch extends DocumentPermission
{
    public $documentName;
    public $username;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id', 'document_id', 'user_id', 'can_view', 'created_at', 'updated_at'], 'integer'],
            [['documentName', 'username'], 'safe'], // for relations
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in parent
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = DocumentPermission::find()
            ->joinWith(['document d', 'user u']); // join with relations

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'pagination' => [
                'pageSize' => 20,
            ],
            'sort' => [
                'defaultOrder' => ['id' => SORT_DESC],
            ],
        ]);

        // enable sorting for related attributes
        $dataProvider->sort->attributes['documentName'] = [
            'asc' => ['d.original_filename' => SORT_ASC],
            'desc' => ['d.original_filename' => SORT_DESC],
        ];
        $dataProvider->sort->attributes['username'] = [
            'asc' => ['u.username' => SORT_ASC],
            'desc' => ['u.username' => SORT_DESC],
        ];

        $this->load($params);

        if (!$this->validate()) {
            return $dataProvider;
        }

        // Grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'document_id' => $this->document_id,
            'user_id' => $this->user_id,
            'can_view' => $this->can_view,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        // Filter by related fields
        $query->andFilterWhere(['like', 'd.original_filename', $this->documentName])
            ->andFilterWhere(['like', 'u.username', $this->username]);

        return $dataProvider;
    }
}
