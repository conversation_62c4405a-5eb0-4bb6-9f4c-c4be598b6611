2025-03-12 10:45:19 [-][-][-][error][yii\db\Exception] PDOException: could not find driver in C:\Web\Reclassering\vendor\yiisoft\yii2\db\Connection.php:722
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\db\Connection.php(722): PDO->__construct()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\db\Connection.php(637): yii\db\Connection->createPdoInstance()
#2 C:\Web\Reclassering\vendor\yiisoft\yii2\db\Connection.php(1067): yii\db\Connection->open()
#3 C:\Web\Reclassering\vendor\yiisoft\yii2\db\Connection.php(1054): yii\db\Connection->getMasterPdo()
#4 C:\Web\Reclassering\vendor\yiisoft\yii2\db\Command.php(261): yii\db\Connection->getSlavePdo()
#5 C:\Web\Reclassering\vendor\yiisoft\yii2\db\Command.php(1181): yii\db\Command->prepare()
#6 C:\Web\Reclassering\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal()
#7 C:\Web\Reclassering\vendor\yiisoft\yii2\db\mysql\Schema.php(366): yii\db\Command->queryAll()
#8 C:\Web\Reclassering\vendor\yiisoft\yii2\db\mysql\Schema.php(134): yii\db\mysql\Schema->findColumns()
#9 C:\Web\Reclassering\vendor\yiisoft\yii2\db\Schema.php(756): yii\db\mysql\Schema->loadTableSchema()
#10 C:\Web\Reclassering\vendor\yiisoft\yii2\db\Schema.php(192): yii\db\Schema->getTableMetadata()
#11 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\MigrateController.php(211): yii\db\Schema->getTableSchema()
#12 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\BaseMigrateController.php(908): yii\console\controllers\MigrateController->getMigrationHistory()
#13 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\BaseMigrateController.php(183): yii\console\controllers\BaseMigrateController->getNewMigrations()
#14 [internal function]: yii\console\controllers\BaseMigrateController->actionUp()
#15 C:\Web\Reclassering\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#16 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#17 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#18 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#19 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#20 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#21 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#22 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#23 {main}

Next yii\db\Exception: could not find driver in C:\Web\Reclassering\vendor\yiisoft\yii2\db\Connection.php:648
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\db\Connection.php(1067): yii\db\Connection->open()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\db\Connection.php(1054): yii\db\Connection->getMasterPdo()
#2 C:\Web\Reclassering\vendor\yiisoft\yii2\db\Command.php(261): yii\db\Connection->getSlavePdo()
#3 C:\Web\Reclassering\vendor\yiisoft\yii2\db\Command.php(1181): yii\db\Command->prepare()
#4 C:\Web\Reclassering\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal()
#5 C:\Web\Reclassering\vendor\yiisoft\yii2\db\mysql\Schema.php(366): yii\db\Command->queryAll()
#6 C:\Web\Reclassering\vendor\yiisoft\yii2\db\mysql\Schema.php(134): yii\db\mysql\Schema->findColumns()
#7 C:\Web\Reclassering\vendor\yiisoft\yii2\db\Schema.php(756): yii\db\mysql\Schema->loadTableSchema()
#8 C:\Web\Reclassering\vendor\yiisoft\yii2\db\Schema.php(192): yii\db\Schema->getTableMetadata()
#9 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\MigrateController.php(211): yii\db\Schema->getTableSchema()
#10 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\BaseMigrateController.php(908): yii\console\controllers\MigrateController->getMigrationHistory()
#11 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\BaseMigrateController.php(183): yii\console\controllers\BaseMigrateController->getNewMigrations()
#12 [internal function]: yii\console\controllers\BaseMigrateController->actionUp()
#13 C:\Web\Reclassering\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#14 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#15 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#16 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#17 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#18 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#19 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#20 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#21 {main}
Additional Information:

2025-03-12 10:45:19 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ACLOCAL_PATH' => '/mingw64/share/aclocal:/usr/share/aclocal'
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_11268_ZVIOHMJWXPIYWYXV'
    'COLORTERM' => 'truecolor'
    'COMMONPROGRAMFILES' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'EGOV-L-046'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CONFIG_SITE' => '/etc/config.site'
    'DISPLAY' => 'needs-to-be-defined'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_10552' => '1'
    'EXEPATH' => 'C:\\Program Files\\Git\\bin'
    'FPS_BROWSER_APP_PROFILE_STRING' => 'Internet Explorer'
    'FPS_BROWSER_USER_PROFILE_STRING' => 'Default'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'HOME' => 'C:\\Users\\<USER>\\Users\\User'
    'HOSTNAME' => 'eGov-L-046'
    'IGCCSVC_DB' => 'AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg=='
    'INFOPATH' => '/mingw64/local/info:/mingw64/share/info:/usr/local/info:/usr/share/info:/usr/info:/share/info'
    'LANG' => 'en_US.UTF-8'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\EGOV-L-046'
    'MANPATH' => '/mingw64/local/man:/mingw64/share/man:/usr/local/man:/usr/share/man:/usr/man:/share/man'
    'MINGW_CHOST' => 'x86_64-w64-mingw32'
    'MINGW_PACKAGE_PREFIX' => 'mingw-w64-x86_64'
    'MINGW_PREFIX' => '/mingw64'
    'MSYSTEM' => 'MINGW64'
    'MSYSTEM_CARCH' => 'x86_64'
    'MSYSTEM_CHOST' => 'x86_64-w64-mingw32'
    'MSYSTEM_PREFIX' => '/mingw64'
    'NUMBER_OF_PROCESSORS' => '12'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OneDriveCommercial' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OnlineServices' => 'Online Services'
    'ORIGINAL_PATH' => 'C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin'
    'ORIGINAL_TEMP' => '/tmp'
    'ORIGINAL_TMP' => '/tmp'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'PATH' => 'C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl;C:\\ProgramData\\ComposerSetup\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC'
    'PKG_CONFIG_PATH' => '/mingw64/lib/pkgconfig:/mingw64/share/pkgconfig'
    'PKG_CONFIG_SYSTEM_INCLUDE_PATH' => '/mingw64/include'
    'PKG_CONFIG_SYSTEM_LIBRARY_PATH' => '/mingw64/lib'
    'platformcode' => 'AN'
    'PLINK_PROTOCOL' => 'ssh'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 186 Stepping 3, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => 'ba03'
    'ProgramData' => 'C:\\ProgramData'
    'PROGRAMFILES' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PS1' => '\\[]633;A\\]\\[\\033]0;$TITLEPREFIX:$PWD\\007\\]\\n\\[\\033[32m\\]\\u@\\h \\[\\033[35m\\]$MSYSTEM \\[\\033[33m\\]\\w\\[\\033[36m\\]`__git_ps1`\\[\\033[0m\\]\\n$ \\[]633;B\\]'
    'PSModulePath' => 'C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe'
    'SHLVL' => '1'
    'SSH_ASKPASS' => '/mingw64/bin/git-askpass.exe'
    'SYSTEMDRIVE' => 'C:'
    'SYSTEMROOT' => 'C:\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.98.0'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMPDIR' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'EGOV-L-046'
    'USERDOMAIN_ROAMINGPROFILE' => 'EGOV-L-046'
    'USERNAME' => 'User'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Users\\User\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-34508b9eb2-sock'
    'WINDIR' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    '_' => '/usr/bin/winpty'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1741787119.0581
    'REQUEST_TIME' => 1741787119
    'argv' => [
        0 => 'yii'
        1 => 'migrate'
    ]
    'argc' => 2
]
2025-04-15 13:37:24 [-][-][-][error][yii\di\NotInstantiableException] ReflectionException: Class "m250415_140747_create_api_user_table" does not exist in C:\Web\Reclassering\vendor\yiisoft\yii2\di\Container.php:507
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\di\Container.php(507): ReflectionClass->__construct()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\di\Container.php(385): yii\di\Container->getDependencies()
#2 C:\Web\Reclassering\vendor\yiisoft\yii2\di\Container.php(170): yii\di\Container->build()
#3 C:\Web\Reclassering\vendor\yiisoft\yii2\BaseYii.php(365): yii\di\Container->get()
#4 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\MigrateController.php(199): yii\BaseYii::createObject()
#5 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\BaseMigrateController.php(757): yii\console\controllers\MigrateController->createMigration()
#6 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\BaseMigrateController.php(216): yii\console\controllers\BaseMigrateController->migrateUp()
#7 [internal function]: yii\console\controllers\BaseMigrateController->actionUp()
#8 C:\Web\Reclassering\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#15 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#16 {main}

Next yii\di\NotInstantiableException: Failed to instantiate component or class "m250415_140747_create_api_user_table". in C:\Web\Reclassering\vendor\yiisoft\yii2\di\Container.php:509
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\di\Container.php(385): yii\di\Container->getDependencies()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\di\Container.php(170): yii\di\Container->build()
#2 C:\Web\Reclassering\vendor\yiisoft\yii2\BaseYii.php(365): yii\di\Container->get()
#3 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\MigrateController.php(199): yii\BaseYii::createObject()
#4 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\BaseMigrateController.php(757): yii\console\controllers\MigrateController->createMigration()
#5 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\BaseMigrateController.php(216): yii\console\controllers\BaseMigrateController->migrateUp()
#6 [internal function]: yii\console\controllers\BaseMigrateController->actionUp()
#7 C:\Web\Reclassering\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#8 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#9 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#10 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#11 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#12 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#13 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#14 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#15 {main}
2025-04-15 13:37:19 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ACLOCAL_PATH' => '/mingw64/share/aclocal:/usr/share/aclocal'
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_1592_PFDGUXZBSWNETBBU'
    'COLORTERM' => 'truecolor'
    'COMMONPROGRAMFILES' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'EGOV-L-046'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CONFIG_SITE' => '/etc/config.site'
    'DISPLAY' => 'needs-to-be-defined'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_32600' => '1'
    'EXEPATH' => 'C:\\Program Files\\Git\\bin'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'HOME' => 'C:\\Users\\<USER>\\Users\\User'
    'HOSTNAME' => 'eGov-L-046'
    'IGCCSVC_DB' => 'AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg=='
    'INFOPATH' => '/mingw64/local/info:/mingw64/share/info:/usr/local/info:/usr/share/info:/usr/info:/share/info'
    'LANG' => 'en_US.UTF-8'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\EGOV-L-046'
    'MANPATH' => '/mingw64/local/man:/mingw64/share/man:/usr/local/man:/usr/share/man:/usr/man:/share/man'
    'MINGW_CHOST' => 'x86_64-w64-mingw32'
    'MINGW_PACKAGE_PREFIX' => 'mingw-w64-x86_64'
    'MINGW_PREFIX' => '/mingw64'
    'MSYSTEM' => 'MINGW64'
    'MSYSTEM_CARCH' => 'x86_64'
    'MSYSTEM_CHOST' => 'x86_64-w64-mingw32'
    'MSYSTEM_PREFIX' => '/mingw64'
    'NUMBER_OF_PROCESSORS' => '12'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OneDriveCommercial' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OnlineServices' => 'Online Services'
    'ORIGINAL_PATH' => 'C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin'
    'ORIGINAL_TEMP' => '/tmp'
    'ORIGINAL_TMP' => '/tmp'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'PATH' => 'C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl;C:\\ProgramData\\ComposerSetup\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC'
    'PKG_CONFIG_PATH' => '/mingw64/lib/pkgconfig:/mingw64/share/pkgconfig'
    'PKG_CONFIG_SYSTEM_INCLUDE_PATH' => '/mingw64/include'
    'PKG_CONFIG_SYSTEM_LIBRARY_PATH' => '/mingw64/lib'
    'platformcode' => 'AN'
    'PLINK_PROTOCOL' => 'ssh'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 186 Stepping 3, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => 'ba03'
    'ProgramData' => 'C:\\ProgramData'
    'PROGRAMFILES' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PS1' => '\\[]633;A\\]\\[\\033]0;$TITLEPREFIX:$PWD\\007\\]\\n\\[\\033[32m\\]\\u@\\h \\[\\033[35m\\]$MSYSTEM \\[\\033[33m\\]\\w\\[\\033[36m\\]`__git_ps1`\\[\\033[0m\\]\\n$ \\[]633;B\\]'
    'PSModulePath' => 'C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe'
    'SHLVL' => '1'
    'SSH_ASKPASS' => '/mingw64/bin/git-askpass.exe'
    'SYSTEMDRIVE' => 'C:'
    'SYSTEMROOT' => 'C:\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.99.1'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMPDIR' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'EGOV-L-046'
    'USERDOMAIN_ROAMINGPROFILE' => 'EGOV-L-046'
    'USERNAME' => 'User'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Users\\User\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-34508b9eb2-sock'
    'WINDIR' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    '_' => '/usr/bin/winpty'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1744735039.5876
    'REQUEST_TIME' => 1744735039
    'argv' => [
        0 => 'yii'
        1 => 'migrate'
    ]
    'argc' => 2
]
2025-04-15 13:41:35 [-][-][-][error][yii\di\NotInstantiableException] ReflectionException: Class "m250415_140747_create_api_user_table" does not exist in C:\Web\Reclassering\vendor\yiisoft\yii2\di\Container.php:507
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\di\Container.php(507): ReflectionClass->__construct()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\di\Container.php(385): yii\di\Container->getDependencies()
#2 C:\Web\Reclassering\vendor\yiisoft\yii2\di\Container.php(170): yii\di\Container->build()
#3 C:\Web\Reclassering\vendor\yiisoft\yii2\BaseYii.php(365): yii\di\Container->get()
#4 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\MigrateController.php(199): yii\BaseYii::createObject()
#5 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\BaseMigrateController.php(757): yii\console\controllers\MigrateController->createMigration()
#6 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\BaseMigrateController.php(216): yii\console\controllers\BaseMigrateController->migrateUp()
#7 [internal function]: yii\console\controllers\BaseMigrateController->actionUp()
#8 C:\Web\Reclassering\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#15 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#16 {main}

Next yii\di\NotInstantiableException: Failed to instantiate component or class "m250415_140747_create_api_user_table". in C:\Web\Reclassering\vendor\yiisoft\yii2\di\Container.php:509
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\di\Container.php(385): yii\di\Container->getDependencies()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\di\Container.php(170): yii\di\Container->build()
#2 C:\Web\Reclassering\vendor\yiisoft\yii2\BaseYii.php(365): yii\di\Container->get()
#3 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\MigrateController.php(199): yii\BaseYii::createObject()
#4 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\BaseMigrateController.php(757): yii\console\controllers\MigrateController->createMigration()
#5 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\BaseMigrateController.php(216): yii\console\controllers\BaseMigrateController->migrateUp()
#6 [internal function]: yii\console\controllers\BaseMigrateController->actionUp()
#7 C:\Web\Reclassering\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#8 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#9 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#10 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#11 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#12 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#13 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#14 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#15 {main}
2025-04-15 13:41:32 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ACLOCAL_PATH' => '/mingw64/share/aclocal:/usr/share/aclocal'
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_1592_PFDGUXZBSWNETBBU'
    'COLORTERM' => 'truecolor'
    'COMMONPROGRAMFILES' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'EGOV-L-046'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CONFIG_SITE' => '/etc/config.site'
    'DISPLAY' => 'needs-to-be-defined'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_32600' => '1'
    'EXEPATH' => 'C:\\Program Files\\Git\\bin'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'HOME' => 'C:\\Users\\<USER>\\Users\\User'
    'HOSTNAME' => 'eGov-L-046'
    'IGCCSVC_DB' => 'AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg=='
    'INFOPATH' => '/mingw64/local/info:/mingw64/share/info:/usr/local/info:/usr/share/info:/usr/info:/share/info'
    'LANG' => 'en_US.UTF-8'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\EGOV-L-046'
    'MANPATH' => '/mingw64/local/man:/mingw64/share/man:/usr/local/man:/usr/share/man:/usr/man:/share/man'
    'MINGW_CHOST' => 'x86_64-w64-mingw32'
    'MINGW_PACKAGE_PREFIX' => 'mingw-w64-x86_64'
    'MINGW_PREFIX' => '/mingw64'
    'MSYSTEM' => 'MINGW64'
    'MSYSTEM_CARCH' => 'x86_64'
    'MSYSTEM_CHOST' => 'x86_64-w64-mingw32'
    'MSYSTEM_PREFIX' => '/mingw64'
    'NUMBER_OF_PROCESSORS' => '12'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OneDriveCommercial' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OnlineServices' => 'Online Services'
    'ORIGINAL_PATH' => 'C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin'
    'ORIGINAL_TEMP' => '/tmp'
    'ORIGINAL_TMP' => '/tmp'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'PATH' => 'C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl;C:\\ProgramData\\ComposerSetup\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC'
    'PKG_CONFIG_PATH' => '/mingw64/lib/pkgconfig:/mingw64/share/pkgconfig'
    'PKG_CONFIG_SYSTEM_INCLUDE_PATH' => '/mingw64/include'
    'PKG_CONFIG_SYSTEM_LIBRARY_PATH' => '/mingw64/lib'
    'platformcode' => 'AN'
    'PLINK_PROTOCOL' => 'ssh'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 186 Stepping 3, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => 'ba03'
    'ProgramData' => 'C:\\ProgramData'
    'PROGRAMFILES' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PS1' => '\\[]633;A\\]\\[\\033]0;$TITLEPREFIX:$PWD\\007\\]\\n\\[\\033[32m\\]\\u@\\h \\[\\033[35m\\]$MSYSTEM \\[\\033[33m\\]\\w\\[\\033[36m\\]`__git_ps1`\\[\\033[0m\\]\\n$ \\[]633;B\\]'
    'PSModulePath' => 'C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe'
    'SHLVL' => '1'
    'SSH_ASKPASS' => '/mingw64/bin/git-askpass.exe'
    'SYSTEMDRIVE' => 'C:'
    'SYSTEMROOT' => 'C:\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.99.1'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMPDIR' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'EGOV-L-046'
    'USERDOMAIN_ROAMINGPROFILE' => 'EGOV-L-046'
    'USERNAME' => 'User'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Users\\User\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-34508b9eb2-sock'
    'WINDIR' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    '_' => '/usr/bin/winpty'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1744735292.043
    'REQUEST_TIME' => 1744735292
    'argv' => [
        0 => 'yii'
        1 => 'migrate'
    ]
    'argc' => 2
]
2025-04-22 10:43:31 [-][-][-][error][yii\console\Exception] yii\console\Exception: The migration name should contain letters, digits, underscore and/or backslash characters only. in C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\BaseMigrateController.php:652
Stack trace:
#0 [internal function]: yii\console\controllers\BaseMigrateController->actionCreate()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#2 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#3 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#4 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#5 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#6 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#7 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#8 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#9 {main}
2025-04-22 10:43:31 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ACLOCAL_PATH' => '/mingw64/share/aclocal:/usr/share/aclocal'
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_15200_TQOZOSRDWPWOBFAX'
    'COLORTERM' => 'truecolor'
    'COMMONPROGRAMFILES' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'EGOV-L-046'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CONFIG_SITE' => '/etc/config.site'
    'DISPLAY' => 'needs-to-be-defined'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_22852' => '1'
    'EXEPATH' => 'C:\\Program Files\\Git\\bin'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'HOME' => 'C:\\Users\\<USER>\\Users\\User'
    'HOSTNAME' => 'eGov-L-046'
    'IGCCSVC_DB' => 'AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg=='
    'INFOPATH' => '/mingw64/local/info:/mingw64/share/info:/usr/local/info:/usr/share/info:/usr/info:/share/info'
    'LANG' => 'en_US.UTF-8'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\EGOV-L-046'
    'MANPATH' => '/mingw64/local/man:/mingw64/share/man:/usr/local/man:/usr/share/man:/usr/man:/share/man'
    'MINGW_CHOST' => 'x86_64-w64-mingw32'
    'MINGW_PACKAGE_PREFIX' => 'mingw-w64-x86_64'
    'MINGW_PREFIX' => '/mingw64'
    'MSYSTEM' => 'MINGW64'
    'MSYSTEM_CARCH' => 'x86_64'
    'MSYSTEM_CHOST' => 'x86_64-w64-mingw32'
    'MSYSTEM_PREFIX' => '/mingw64'
    'NUMBER_OF_PROCESSORS' => '12'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OneDriveCommercial' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OnlineServices' => 'Online Services'
    'ORIGINAL_PATH' => 'C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin'
    'ORIGINAL_TEMP' => '/tmp'
    'ORIGINAL_TMP' => '/tmp'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'PATH' => 'C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl;C:\\ProgramData\\ComposerSetup\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC'
    'PKG_CONFIG_PATH' => '/mingw64/lib/pkgconfig:/mingw64/share/pkgconfig'
    'PKG_CONFIG_SYSTEM_INCLUDE_PATH' => '/mingw64/include'
    'PKG_CONFIG_SYSTEM_LIBRARY_PATH' => '/mingw64/lib'
    'platformcode' => 'AN'
    'PLINK_PROTOCOL' => 'ssh'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 186 Stepping 3, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => 'ba03'
    'ProgramData' => 'C:\\ProgramData'
    'PROGRAMFILES' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PS1' => '\\[]633;A\\]\\[\\033]0;$TITLEPREFIX:$PWD\\007\\]\\n\\[\\033[32m\\]\\u@\\h \\[\\033[35m\\]$MSYSTEM \\[\\033[33m\\]\\w\\[\\033[36m\\]`__git_ps1`\\[\\033[0m\\]\\n$ \\[]633;B\\]'
    'PSModulePath' => 'C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe'
    'SHLVL' => '1'
    'SSH_ASKPASS' => '/mingw64/bin/git-askpass.exe'
    'SYSTEMDRIVE' => 'C:'
    'SYSTEMROOT' => 'C:\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.99.3'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMPDIR' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'EGOV-L-046'
    'USERDOMAIN_ROAMINGPROFILE' => 'EGOV-L-046'
    'USERNAME' => 'User'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Users\\User\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-34508b9eb2-sock'
    'WINDIR' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    '_' => '/usr/bin/winpty'
    '__COMPAT_LAYER' => 'DetectorsAppHealth'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => **********.8146
    'REQUEST_TIME' => **********
    'argv' => [
        0 => 'yii'
        1 => 'migrate/create'
        2 => 'rapport-workflow'
    ]
    'argc' => 3
]
2025-04-22 11:51:58 [-][-][-][error][yii\console\UnknownCommandException] yii\base\InvalidRouteException: Unable to resolve the request "mirgrate". in C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php:561
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#2 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#3 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#4 {main}

Next yii\console\UnknownCommandException: Unknown command "mirgrate". in C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php:183
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#2 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#3 {main}
2025-04-22 11:51:58 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ACLOCAL_PATH' => '/mingw64/share/aclocal:/usr/share/aclocal'
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_15200_TQOZOSRDWPWOBFAX'
    'COLORTERM' => 'truecolor'
    'COMMONPROGRAMFILES' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'EGOV-L-046'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CONFIG_SITE' => '/etc/config.site'
    'DISPLAY' => 'needs-to-be-defined'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_22852' => '1'
    'EXEPATH' => 'C:\\Program Files\\Git\\bin'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'HOME' => 'C:\\Users\\<USER>\\Users\\User'
    'HOSTNAME' => 'eGov-L-046'
    'IGCCSVC_DB' => 'AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg=='
    'INFOPATH' => '/mingw64/local/info:/mingw64/share/info:/usr/local/info:/usr/share/info:/usr/info:/share/info'
    'LANG' => 'en_US.UTF-8'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\EGOV-L-046'
    'MANPATH' => '/mingw64/local/man:/mingw64/share/man:/usr/local/man:/usr/share/man:/usr/man:/share/man'
    'MINGW_CHOST' => 'x86_64-w64-mingw32'
    'MINGW_PACKAGE_PREFIX' => 'mingw-w64-x86_64'
    'MINGW_PREFIX' => '/mingw64'
    'MSYSTEM' => 'MINGW64'
    'MSYSTEM_CARCH' => 'x86_64'
    'MSYSTEM_CHOST' => 'x86_64-w64-mingw32'
    'MSYSTEM_PREFIX' => '/mingw64'
    'NUMBER_OF_PROCESSORS' => '12'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OneDriveCommercial' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OnlineServices' => 'Online Services'
    'ORIGINAL_PATH' => 'C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin'
    'ORIGINAL_TEMP' => '/tmp'
    'ORIGINAL_TMP' => '/tmp'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'PATH' => 'C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl;C:\\ProgramData\\ComposerSetup\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC'
    'PKG_CONFIG_PATH' => '/mingw64/lib/pkgconfig:/mingw64/share/pkgconfig'
    'PKG_CONFIG_SYSTEM_INCLUDE_PATH' => '/mingw64/include'
    'PKG_CONFIG_SYSTEM_LIBRARY_PATH' => '/mingw64/lib'
    'platformcode' => 'AN'
    'PLINK_PROTOCOL' => 'ssh'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 186 Stepping 3, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => 'ba03'
    'ProgramData' => 'C:\\ProgramData'
    'PROGRAMFILES' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PS1' => '\\[]633;A\\]\\[\\033]0;$TITLEPREFIX:$PWD\\007\\]\\n\\[\\033[32m\\]\\u@\\h \\[\\033[35m\\]$MSYSTEM \\[\\033[33m\\]\\w\\[\\033[36m\\]`__git_ps1`\\[\\033[0m\\]\\n$ \\[]633;B\\]'
    'PSModulePath' => 'C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe'
    'SHLVL' => '1'
    'SSH_ASKPASS' => '/mingw64/bin/git-askpass.exe'
    'SYSTEMDRIVE' => 'C:'
    'SYSTEMROOT' => 'C:\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.99.3'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMPDIR' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'EGOV-L-046'
    'USERDOMAIN_ROAMINGPROFILE' => 'EGOV-L-046'
    'USERNAME' => 'User'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Users\\User\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-34508b9eb2-sock'
    'WINDIR' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    '_' => '/usr/bin/winpty'
    '__COMPAT_LAYER' => 'DetectorsAppHealth'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => **********.4627
    'REQUEST_TIME' => **********
    'argv' => [
        0 => 'yii'
        1 => 'mirgrate'
    ]
    'argc' => 2
]
2025-06-25 18:04:57 [-][-][-][error][yii\base\InvalidArgumentException] yii\base\InvalidArgumentException: Invalid path alias: @bedezign/audit/migrations in C:\Web\Reclassering\vendor\yiisoft\yii2\BaseYii.php:154
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\BaseMigrateController.php(144): yii\BaseYii::getAlias()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\MigrateController.php(182): yii\console\controllers\BaseMigrateController->beforeAction()
#2 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Controller.php(176): yii\console\controllers\MigrateController->beforeAction()
#3 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#4 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#5 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#6 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#7 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(385): yii\console\Application->handleRequest()
#8 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#9 {main}
2025-06-25 18:04:57 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ACLOCAL_PATH' => '/mingw64/share/aclocal:/usr/share/aclocal'
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_1284_BJCDMGNZVJOVAGCV'
    'COLORTERM' => 'truecolor'
    'COMMONPROGRAMFILES' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'EGOV-L-046'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CONFIG_SITE' => '/etc/config.site'
    'DISPLAY' => 'needs-to-be-defined'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_8184_1262719628' => '1'
    'EFC_8184_1592913036' => '1'
    'EFC_8184_2283032206' => '1'
    'EFC_8184_2775293581' => '1'
    'EFC_8184_3789132940' => '1'
    'EXEPATH' => 'C:\\Program Files\\Git\\bin'
    'FPS_BROWSER_APP_PROFILE_STRING' => 'Internet Explorer'
    'FPS_BROWSER_USER_PROFILE_STRING' => 'Default'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'HOME' => 'C:\\Users\\<USER>\\Users\\User'
    'HOSTNAME' => 'eGov-L-046'
    'IGCCSVC_DB' => 'AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg=='
    'INFOPATH' => '/mingw64/local/info:/mingw64/share/info:/usr/local/info:/usr/share/info:/usr/info:/share/info'
    'LANG' => 'en_US.UTF-8'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\EGOV-L-046'
    'MANPATH' => '/mingw64/local/man:/mingw64/share/man:/usr/local/man:/usr/share/man:/usr/man:/share/man'
    'MINGW_CHOST' => 'x86_64-w64-mingw32'
    'MINGW_PACKAGE_PREFIX' => 'mingw-w64-x86_64'
    'MINGW_PREFIX' => '/mingw64'
    'MSYSTEM' => 'MINGW64'
    'MSYSTEM_CARCH' => 'x86_64'
    'MSYSTEM_CHOST' => 'x86_64-w64-mingw32'
    'MSYSTEM_PREFIX' => '/mingw64'
    'NUMBER_OF_PROCESSORS' => '12'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OneDriveCommercial' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OnlineServices' => 'Online Services'
    'ORIGINAL_PATH' => 'C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand'
    'ORIGINAL_TEMP' => '/tmp'
    'ORIGINAL_TMP' => '/tmp'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'PATH' => 'C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl;C:\\ProgramData\\ComposerSetup\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC'
    'PKG_CONFIG_PATH' => '/mingw64/lib/pkgconfig:/mingw64/share/pkgconfig'
    'PKG_CONFIG_SYSTEM_INCLUDE_PATH' => '/mingw64/include'
    'PKG_CONFIG_SYSTEM_LIBRARY_PATH' => '/mingw64/lib'
    'platformcode' => 'AN'
    'PLINK_PROTOCOL' => 'ssh'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 186 Stepping 3, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => 'ba03'
    'ProgramData' => 'C:\\ProgramData'
    'PROGRAMFILES' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PS1' => '\\[]633;A\\]\\[\\033]0;$TITLEPREFIX:$PWD\\007\\]\\n\\[\\033[32m\\]\\u@\\h \\[\\033[35m\\]$MSYSTEM \\[\\033[33m\\]\\w\\[\\033[36m\\]`__git_ps1`\\[\\033[0m\\]\\n$ \\[]633;B\\]'
    'PSModulePath' => 'C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe'
    'SHLVL' => '1'
    'SSH_ASKPASS' => '/mingw64/bin/git-askpass.exe'
    'SYSTEMDRIVE' => 'C:'
    'SYSTEMROOT' => 'C:\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.101.1'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMPDIR' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'EGOV-L-046'
    'USERDOMAIN_ROAMINGPROFILE' => 'EGOV-L-046'
    'USERNAME' => 'User'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Users\\User\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-34508b9eb2-sock'
    'WINDIR' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    '_' => '/usr/bin/winpty'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1750871096.9975
    'REQUEST_TIME' => 1750871096
    'argv' => [
        0 => 'yii'
        1 => 'migrate'
        2 => '--migrationPath=@bedezign/audit/migrations'
    ]
    'argc' => 3
]
2025-06-25 18:05:30 [-][-][-][error][yii\console\UnknownCommandException] yii\base\InvalidRouteException: Unable to resolve the request: audit/migrate in C:\Web\Reclassering\vendor\yiisoft\yii2\base\Controller.php:149
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#2 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#3 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#4 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(385): yii\console\Application->handleRequest()
#5 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#6 {main}

Next yii\console\UnknownCommandException: Unknown command "audit/migrate". in C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php:183
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(385): yii\console\Application->handleRequest()
#2 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#3 {main}
2025-06-25 18:05:30 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ACLOCAL_PATH' => '/mingw64/share/aclocal:/usr/share/aclocal'
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_1284_BJCDMGNZVJOVAGCV'
    'COLORTERM' => 'truecolor'
    'COMMONPROGRAMFILES' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'EGOV-L-046'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CONFIG_SITE' => '/etc/config.site'
    'DISPLAY' => 'needs-to-be-defined'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_8184_1262719628' => '1'
    'EFC_8184_1592913036' => '1'
    'EFC_8184_2283032206' => '1'
    'EFC_8184_2775293581' => '1'
    'EFC_8184_3789132940' => '1'
    'EXEPATH' => 'C:\\Program Files\\Git\\bin'
    'FPS_BROWSER_APP_PROFILE_STRING' => 'Internet Explorer'
    'FPS_BROWSER_USER_PROFILE_STRING' => 'Default'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'HOME' => 'C:\\Users\\<USER>\\Users\\User'
    'HOSTNAME' => 'eGov-L-046'
    'IGCCSVC_DB' => 'AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg=='
    'INFOPATH' => '/mingw64/local/info:/mingw64/share/info:/usr/local/info:/usr/share/info:/usr/info:/share/info'
    'LANG' => 'en_US.UTF-8'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\EGOV-L-046'
    'MANPATH' => '/mingw64/local/man:/mingw64/share/man:/usr/local/man:/usr/share/man:/usr/man:/share/man'
    'MINGW_CHOST' => 'x86_64-w64-mingw32'
    'MINGW_PACKAGE_PREFIX' => 'mingw-w64-x86_64'
    'MINGW_PREFIX' => '/mingw64'
    'MSYSTEM' => 'MINGW64'
    'MSYSTEM_CARCH' => 'x86_64'
    'MSYSTEM_CHOST' => 'x86_64-w64-mingw32'
    'MSYSTEM_PREFIX' => '/mingw64'
    'NUMBER_OF_PROCESSORS' => '12'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OneDriveCommercial' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OnlineServices' => 'Online Services'
    'ORIGINAL_PATH' => 'C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand'
    'ORIGINAL_TEMP' => '/tmp'
    'ORIGINAL_TMP' => '/tmp'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'PATH' => 'C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl;C:\\ProgramData\\ComposerSetup\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC'
    'PKG_CONFIG_PATH' => '/mingw64/lib/pkgconfig:/mingw64/share/pkgconfig'
    'PKG_CONFIG_SYSTEM_INCLUDE_PATH' => '/mingw64/include'
    'PKG_CONFIG_SYSTEM_LIBRARY_PATH' => '/mingw64/lib'
    'platformcode' => 'AN'
    'PLINK_PROTOCOL' => 'ssh'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 186 Stepping 3, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => 'ba03'
    'ProgramData' => 'C:\\ProgramData'
    'PROGRAMFILES' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PS1' => '\\[]633;A\\]\\[\\033]0;$TITLEPREFIX:$PWD\\007\\]\\n\\[\\033[32m\\]\\u@\\h \\[\\033[35m\\]$MSYSTEM \\[\\033[33m\\]\\w\\[\\033[36m\\]`__git_ps1`\\[\\033[0m\\]\\n$ \\[]633;B\\]'
    'PSModulePath' => 'C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe'
    'SHLVL' => '1'
    'SSH_ASKPASS' => '/mingw64/bin/git-askpass.exe'
    'SYSTEMDRIVE' => 'C:'
    'SYSTEMROOT' => 'C:\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.101.1'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMPDIR' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'EGOV-L-046'
    'USERDOMAIN_ROAMINGPROFILE' => 'EGOV-L-046'
    'USERNAME' => 'User'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Users\\User\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-34508b9eb2-sock'
    'WINDIR' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    '_' => '/usr/bin/winpty'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1750871130.8892
    'REQUEST_TIME' => 1750871130
    'argv' => [
        0 => 'yii'
        1 => 'audit/migrate'
    ]
    'argc' => 2
]
2025-06-25 18:06:06 [-][-][-][error][yii\base\InvalidArgumentException] yii\base\InvalidArgumentException: Invalid path alias: @bedezign/audit/migrations in C:\Web\Reclassering\vendor\yiisoft\yii2\BaseYii.php:154
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\BaseMigrateController.php(144): yii\BaseYii::getAlias()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\MigrateController.php(182): yii\console\controllers\BaseMigrateController->beforeAction()
#2 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Controller.php(176): yii\console\controllers\MigrateController->beforeAction()
#3 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#4 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#5 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#6 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#7 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(385): yii\console\Application->handleRequest()
#8 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#9 {main}
2025-06-25 18:06:06 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ACLOCAL_PATH' => '/mingw64/share/aclocal:/usr/share/aclocal'
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_1284_BJCDMGNZVJOVAGCV'
    'COLORTERM' => 'truecolor'
    'COMMONPROGRAMFILES' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'EGOV-L-046'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CONFIG_SITE' => '/etc/config.site'
    'DISPLAY' => 'needs-to-be-defined'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_8184_1262719628' => '1'
    'EFC_8184_1592913036' => '1'
    'EFC_8184_2283032206' => '1'
    'EFC_8184_2775293581' => '1'
    'EFC_8184_3789132940' => '1'
    'EXEPATH' => 'C:\\Program Files\\Git\\bin'
    'FPS_BROWSER_APP_PROFILE_STRING' => 'Internet Explorer'
    'FPS_BROWSER_USER_PROFILE_STRING' => 'Default'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'HOME' => 'C:\\Users\\<USER>\\Users\\User'
    'HOSTNAME' => 'eGov-L-046'
    'IGCCSVC_DB' => 'AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg=='
    'INFOPATH' => '/mingw64/local/info:/mingw64/share/info:/usr/local/info:/usr/share/info:/usr/info:/share/info'
    'LANG' => 'en_US.UTF-8'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\EGOV-L-046'
    'MANPATH' => '/mingw64/local/man:/mingw64/share/man:/usr/local/man:/usr/share/man:/usr/man:/share/man'
    'MINGW_CHOST' => 'x86_64-w64-mingw32'
    'MINGW_PACKAGE_PREFIX' => 'mingw-w64-x86_64'
    'MINGW_PREFIX' => '/mingw64'
    'MSYSTEM' => 'MINGW64'
    'MSYSTEM_CARCH' => 'x86_64'
    'MSYSTEM_CHOST' => 'x86_64-w64-mingw32'
    'MSYSTEM_PREFIX' => '/mingw64'
    'NUMBER_OF_PROCESSORS' => '12'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OneDriveCommercial' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OnlineServices' => 'Online Services'
    'ORIGINAL_PATH' => 'C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand'
    'ORIGINAL_TEMP' => '/tmp'
    'ORIGINAL_TMP' => '/tmp'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'PATH' => 'C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl;C:\\ProgramData\\ComposerSetup\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC'
    'PKG_CONFIG_PATH' => '/mingw64/lib/pkgconfig:/mingw64/share/pkgconfig'
    'PKG_CONFIG_SYSTEM_INCLUDE_PATH' => '/mingw64/include'
    'PKG_CONFIG_SYSTEM_LIBRARY_PATH' => '/mingw64/lib'
    'platformcode' => 'AN'
    'PLINK_PROTOCOL' => 'ssh'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 186 Stepping 3, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => 'ba03'
    'ProgramData' => 'C:\\ProgramData'
    'PROGRAMFILES' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PS1' => '\\[]633;A\\]\\[\\033]0;$TITLEPREFIX:$PWD\\007\\]\\n\\[\\033[32m\\]\\u@\\h \\[\\033[35m\\]$MSYSTEM \\[\\033[33m\\]\\w\\[\\033[36m\\]`__git_ps1`\\[\\033[0m\\]\\n$ \\[]633;B\\]'
    'PSModulePath' => 'C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe'
    'SHLVL' => '1'
    'SSH_ASKPASS' => '/mingw64/bin/git-askpass.exe'
    'SYSTEMDRIVE' => 'C:'
    'SYSTEMROOT' => 'C:\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.101.1'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMPDIR' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'EGOV-L-046'
    'USERDOMAIN_ROAMINGPROFILE' => 'EGOV-L-046'
    'USERNAME' => 'User'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Users\\User\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-34508b9eb2-sock'
    'WINDIR' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    '_' => '/usr/bin/winpty'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1750871166.1808
    'REQUEST_TIME' => 1750871166
    'argv' => [
        0 => 'yii'
        1 => 'migrate'
        2 => '--migrationPath=@bedezign/audit/migrations'
    ]
    'argc' => 3
]
2025-06-25 18:09:23 [-][-][-][error][yii\di\NotInstantiableException] ReflectionException: Class "m150626_000001_create_audit_entry" does not exist in C:\Web\Reclassering\vendor\yiisoft\yii2\di\Container.php:507
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\di\Container.php(507): ReflectionClass->__construct()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\di\Container.php(385): yii\di\Container->getDependencies()
#2 C:\Web\Reclassering\vendor\yiisoft\yii2\di\Container.php(170): yii\di\Container->build()
#3 C:\Web\Reclassering\vendor\yiisoft\yii2\BaseYii.php(365): yii\di\Container->get()
#4 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\MigrateController.php(199): yii\BaseYii::createObject()
#5 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\BaseMigrateController.php(757): yii\console\controllers\MigrateController->createMigration()
#6 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\BaseMigrateController.php(216): yii\console\controllers\BaseMigrateController->migrateUp()
#7 [internal function]: yii\console\controllers\BaseMigrateController->actionUp()
#8 C:\Web\Reclassering\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(385): yii\console\Application->handleRequest()
#15 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#16 {main}

Next yii\di\NotInstantiableException: Failed to instantiate component or class "m150626_000001_create_audit_entry". in C:\Web\Reclassering\vendor\yiisoft\yii2\di\Container.php:509
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\di\Container.php(385): yii\di\Container->getDependencies()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\di\Container.php(170): yii\di\Container->build()
#2 C:\Web\Reclassering\vendor\yiisoft\yii2\BaseYii.php(365): yii\di\Container->get()
#3 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\MigrateController.php(199): yii\BaseYii::createObject()
#4 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\BaseMigrateController.php(757): yii\console\controllers\MigrateController->createMigration()
#5 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\BaseMigrateController.php(216): yii\console\controllers\BaseMigrateController->migrateUp()
#6 [internal function]: yii\console\controllers\BaseMigrateController->actionUp()
#7 C:\Web\Reclassering\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#8 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#9 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#10 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#11 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#12 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#13 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(385): yii\console\Application->handleRequest()
#14 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#15 {main}
2025-06-25 18:09:15 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ACLOCAL_PATH' => '/mingw64/share/aclocal:/usr/share/aclocal'
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_1284_BJCDMGNZVJOVAGCV'
    'COLORTERM' => 'truecolor'
    'COMMONPROGRAMFILES' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'EGOV-L-046'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CONFIG_SITE' => '/etc/config.site'
    'DISPLAY' => 'needs-to-be-defined'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_8184_1262719628' => '1'
    'EFC_8184_1592913036' => '1'
    'EFC_8184_2283032206' => '1'
    'EFC_8184_2775293581' => '1'
    'EFC_8184_3789132940' => '1'
    'EXEPATH' => 'C:\\Program Files\\Git\\bin'
    'FPS_BROWSER_APP_PROFILE_STRING' => 'Internet Explorer'
    'FPS_BROWSER_USER_PROFILE_STRING' => 'Default'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'HOME' => 'C:\\Users\\<USER>\\Users\\User'
    'HOSTNAME' => 'eGov-L-046'
    'IGCCSVC_DB' => 'AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg=='
    'INFOPATH' => '/mingw64/local/info:/mingw64/share/info:/usr/local/info:/usr/share/info:/usr/info:/share/info'
    'LANG' => 'en_US.UTF-8'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\EGOV-L-046'
    'MANPATH' => '/mingw64/local/man:/mingw64/share/man:/usr/local/man:/usr/share/man:/usr/man:/share/man'
    'MINGW_CHOST' => 'x86_64-w64-mingw32'
    'MINGW_PACKAGE_PREFIX' => 'mingw-w64-x86_64'
    'MINGW_PREFIX' => '/mingw64'
    'MSYSTEM' => 'MINGW64'
    'MSYSTEM_CARCH' => 'x86_64'
    'MSYSTEM_CHOST' => 'x86_64-w64-mingw32'
    'MSYSTEM_PREFIX' => '/mingw64'
    'NUMBER_OF_PROCESSORS' => '12'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OneDriveCommercial' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OnlineServices' => 'Online Services'
    'ORIGINAL_PATH' => 'C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand'
    'ORIGINAL_TEMP' => '/tmp'
    'ORIGINAL_TMP' => '/tmp'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'PATH' => 'C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl;C:\\ProgramData\\ComposerSetup\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC'
    'PKG_CONFIG_PATH' => '/mingw64/lib/pkgconfig:/mingw64/share/pkgconfig'
    'PKG_CONFIG_SYSTEM_INCLUDE_PATH' => '/mingw64/include'
    'PKG_CONFIG_SYSTEM_LIBRARY_PATH' => '/mingw64/lib'
    'platformcode' => 'AN'
    'PLINK_PROTOCOL' => 'ssh'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 186 Stepping 3, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => 'ba03'
    'ProgramData' => 'C:\\ProgramData'
    'PROGRAMFILES' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PS1' => '\\[]633;A\\]\\[\\033]0;$TITLEPREFIX:$PWD\\007\\]\\n\\[\\033[32m\\]\\u@\\h \\[\\033[35m\\]$MSYSTEM \\[\\033[33m\\]\\w\\[\\033[36m\\]`__git_ps1`\\[\\033[0m\\]\\n$ \\[]633;B\\]'
    'PSModulePath' => 'C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe'
    'SHLVL' => '1'
    'SSH_ASKPASS' => '/mingw64/bin/git-askpass.exe'
    'SYSTEMDRIVE' => 'C:'
    'SYSTEMROOT' => 'C:\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.101.1'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMPDIR' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'EGOV-L-046'
    'USERDOMAIN_ROAMINGPROFILE' => 'EGOV-L-046'
    'USERNAME' => 'User'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Users\\User\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-34508b9eb2-sock'
    'WINDIR' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    '_' => '/usr/bin/winpty'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1750871355.3515
    'REQUEST_TIME' => 1750871355
    'argv' => [
        0 => 'yii'
        1 => 'migrate'
        2 => '--migrationPath=@vendor/bedezign/yii2-audit/src/migrations'
    ]
    'argc' => 3
]
2025-06-30 13:52:36 [-][-][-][error][yii\base\InvalidArgumentException] yii\base\InvalidArgumentException: Invalid path alias: @bedezign/audit/migrations in C:\Web\Reclassering\vendor\yiisoft\yii2\BaseYii.php:154
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\BaseMigrateController.php(144): yii\BaseYii::getAlias()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\MigrateController.php(182): yii\console\controllers\BaseMigrateController->beforeAction()
#2 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Controller.php(176): yii\console\controllers\MigrateController->beforeAction()
#3 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#4 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#5 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#6 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#7 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(385): yii\console\Application->handleRequest()
#8 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#9 {main}
2025-06-30 13:52:36 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ACLOCAL_PATH' => '/mingw64/share/aclocal:/usr/share/aclocal'
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_2856_PIQMWZDBHSGDKVAS'
    'COLORTERM' => 'truecolor'
    'COMMONPROGRAMFILES' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'EGOV-L-046'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CONFIG_SITE' => '/etc/config.site'
    'DISPLAY' => 'needs-to-be-defined'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_12604_1262719628' => '1'
    'EFC_12604_1592913036' => '1'
    'EFC_12604_2283032206' => '1'
    'EFC_12604_2775293581' => '1'
    'EFC_12604_3789132940' => '1'
    'EXEPATH' => 'C:\\Program Files\\Git\\bin'
    'FPS_BROWSER_APP_PROFILE_STRING' => 'Internet Explorer'
    'FPS_BROWSER_USER_PROFILE_STRING' => 'Default'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'HOME' => 'C:\\Users\\<USER>\\Users\\User'
    'HOSTNAME' => 'eGov-L-046'
    'IGCCSVC_DB' => 'AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg=='
    'INFOPATH' => '/mingw64/local/info:/mingw64/share/info:/usr/local/info:/usr/share/info:/usr/info:/share/info'
    'LANG' => 'en_US.UTF-8'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\EGOV-L-046'
    'MANPATH' => '/mingw64/local/man:/mingw64/share/man:/usr/local/man:/usr/share/man:/usr/man:/share/man'
    'MINGW_CHOST' => 'x86_64-w64-mingw32'
    'MINGW_PACKAGE_PREFIX' => 'mingw-w64-x86_64'
    'MINGW_PREFIX' => '/mingw64'
    'MSYSTEM' => 'MINGW64'
    'MSYSTEM_CARCH' => 'x86_64'
    'MSYSTEM_CHOST' => 'x86_64-w64-mingw32'
    'MSYSTEM_PREFIX' => '/mingw64'
    'NUMBER_OF_PROCESSORS' => '12'
    'OLDPWD' => '/c/Web/Reclassering/backend'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OneDriveCommercial' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OnlineServices' => 'Online Services'
    'ORIGINAL_PATH' => 'C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand'
    'ORIGINAL_TEMP' => '/tmp'
    'ORIGINAL_TMP' => '/tmp'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'PATH' => 'C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl;C:\\ProgramData\\ComposerSetup\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC'
    'PKG_CONFIG_PATH' => '/mingw64/lib/pkgconfig:/mingw64/share/pkgconfig'
    'PKG_CONFIG_SYSTEM_INCLUDE_PATH' => '/mingw64/include'
    'PKG_CONFIG_SYSTEM_LIBRARY_PATH' => '/mingw64/lib'
    'platformcode' => 'AN'
    'PLINK_PROTOCOL' => 'ssh'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 186 Stepping 3, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => 'ba03'
    'ProgramData' => 'C:\\ProgramData'
    'PROGRAMFILES' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PS1' => '\\[]633;A\\]\\[\\033]0;$TITLEPREFIX:$PWD\\007\\]\\n\\[\\033[32m\\]\\u@\\h \\[\\033[35m\\]$MSYSTEM \\[\\033[33m\\]\\w\\[\\033[36m\\]`__git_ps1`\\[\\033[0m\\]\\n$ \\[]633;B\\]'
    'PSModulePath' => 'C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe'
    'SHLVL' => '1'
    'SSH_ASKPASS' => '/mingw64/bin/git-askpass.exe'
    'SYSTEMDRIVE' => 'C:'
    'SYSTEMROOT' => 'C:\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.101.2'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMPDIR' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'EGOV-L-046'
    'USERDOMAIN_ROAMINGPROFILE' => 'EGOV-L-046'
    'USERNAME' => 'User'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Users\\User\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-34508b9eb2-sock'
    'WINDIR' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    '_' => '/usr/bin/winpty'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1751287956.3037
    'REQUEST_TIME' => 1751287956
    'argv' => [
        0 => 'yii'
        1 => 'migrate'
        2 => '--migrationPath=@bedezign/audit/migrations'
    ]
    'argc' => 3
]
2025-06-30 13:52:48 [-][-][-][error][yii\base\InvalidArgumentException] yii\base\InvalidArgumentException: Invalid path alias: @bedezign/audit/src/migrations in C:\Web\Reclassering\vendor\yiisoft\yii2\BaseYii.php:154
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\BaseMigrateController.php(144): yii\BaseYii::getAlias()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\MigrateController.php(182): yii\console\controllers\BaseMigrateController->beforeAction()
#2 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Controller.php(176): yii\console\controllers\MigrateController->beforeAction()
#3 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#4 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#5 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#6 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#7 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(385): yii\console\Application->handleRequest()
#8 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#9 {main}
2025-06-30 13:52:48 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ACLOCAL_PATH' => '/mingw64/share/aclocal:/usr/share/aclocal'
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_2856_PIQMWZDBHSGDKVAS'
    'COLORTERM' => 'truecolor'
    'COMMONPROGRAMFILES' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'EGOV-L-046'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CONFIG_SITE' => '/etc/config.site'
    'DISPLAY' => 'needs-to-be-defined'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_12604_1262719628' => '1'
    'EFC_12604_1592913036' => '1'
    'EFC_12604_2283032206' => '1'
    'EFC_12604_2775293581' => '1'
    'EFC_12604_3789132940' => '1'
    'EXEPATH' => 'C:\\Program Files\\Git\\bin'
    'FPS_BROWSER_APP_PROFILE_STRING' => 'Internet Explorer'
    'FPS_BROWSER_USER_PROFILE_STRING' => 'Default'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'HOME' => 'C:\\Users\\<USER>\\Users\\User'
    'HOSTNAME' => 'eGov-L-046'
    'IGCCSVC_DB' => 'AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg=='
    'INFOPATH' => '/mingw64/local/info:/mingw64/share/info:/usr/local/info:/usr/share/info:/usr/info:/share/info'
    'LANG' => 'en_US.UTF-8'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\EGOV-L-046'
    'MANPATH' => '/mingw64/local/man:/mingw64/share/man:/usr/local/man:/usr/share/man:/usr/man:/share/man'
    'MINGW_CHOST' => 'x86_64-w64-mingw32'
    'MINGW_PACKAGE_PREFIX' => 'mingw-w64-x86_64'
    'MINGW_PREFIX' => '/mingw64'
    'MSYSTEM' => 'MINGW64'
    'MSYSTEM_CARCH' => 'x86_64'
    'MSYSTEM_CHOST' => 'x86_64-w64-mingw32'
    'MSYSTEM_PREFIX' => '/mingw64'
    'NUMBER_OF_PROCESSORS' => '12'
    'OLDPWD' => '/c/Web/Reclassering/backend'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OneDriveCommercial' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OnlineServices' => 'Online Services'
    'ORIGINAL_PATH' => 'C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand'
    'ORIGINAL_TEMP' => '/tmp'
    'ORIGINAL_TMP' => '/tmp'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'PATH' => 'C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl;C:\\ProgramData\\ComposerSetup\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC'
    'PKG_CONFIG_PATH' => '/mingw64/lib/pkgconfig:/mingw64/share/pkgconfig'
    'PKG_CONFIG_SYSTEM_INCLUDE_PATH' => '/mingw64/include'
    'PKG_CONFIG_SYSTEM_LIBRARY_PATH' => '/mingw64/lib'
    'platformcode' => 'AN'
    'PLINK_PROTOCOL' => 'ssh'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 186 Stepping 3, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => 'ba03'
    'ProgramData' => 'C:\\ProgramData'
    'PROGRAMFILES' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PS1' => '\\[]633;A\\]\\[\\033]0;$TITLEPREFIX:$PWD\\007\\]\\n\\[\\033[32m\\]\\u@\\h \\[\\033[35m\\]$MSYSTEM \\[\\033[33m\\]\\w\\[\\033[36m\\]`__git_ps1`\\[\\033[0m\\]\\n$ \\[]633;B\\]'
    'PSModulePath' => 'C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe'
    'SHLVL' => '1'
    'SSH_ASKPASS' => '/mingw64/bin/git-askpass.exe'
    'SYSTEMDRIVE' => 'C:'
    'SYSTEMROOT' => 'C:\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.101.2'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMPDIR' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'EGOV-L-046'
    'USERDOMAIN_ROAMINGPROFILE' => 'EGOV-L-046'
    'USERNAME' => 'User'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Users\\User\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-34508b9eb2-sock'
    'WINDIR' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    '_' => '/usr/bin/winpty'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1751287968.2951
    'REQUEST_TIME' => 1751287968
    'argv' => [
        0 => 'yii'
        1 => 'migrate'
        2 => '--migrationPath=@bedezign/audit/src/migrations'
    ]
    'argc' => 3
]
2025-06-30 14:56:48 [-][-][-][error][yii\base\UnknownPropertyException] yii\base\UnknownPropertyException: Setting unknown property: bedezign\yii2\audit\Audit::enabed in C:\Web\Reclassering\vendor\yiisoft\yii2\base\Component.php:221
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\BaseYii.php(557): yii\base\Component->__set()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\base\BaseObject.php(107): yii\BaseYii::configure()
#2 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(161): yii\base\BaseObject->__construct()
#3 [internal function]: yii\base\Module->__construct()
#4 C:\Web\Reclassering\vendor\yiisoft\yii2\di\Container.php(419): ReflectionClass->newInstanceArgs()
#5 C:\Web\Reclassering\vendor\yiisoft\yii2\di\Container.php(170): yii\di\Container->build()
#6 C:\Web\Reclassering\vendor\yiisoft\yii2\BaseYii.php(365): yii\di\Container->get()
#7 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(445): yii\BaseYii::createObject()
#8 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(314): yii\base\Module->getModule()
#9 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(272): yii\base\Application->bootstrap()
#10 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(124): yii\base\Application->init()
#11 C:\Web\Reclassering\vendor\yiisoft\yii2\base\BaseObject.php(109): yii\console\Application->init()
#12 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(205): yii\base\BaseObject->__construct()
#13 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(89): yii\base\Application->__construct()
#14 C:\Web\Reclassering\yii(22): yii\console\Application->__construct()
#15 {main}
2025-06-30 14:56:48 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ACLOCAL_PATH' => '/mingw64/share/aclocal:/usr/share/aclocal'
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_2856_PIQMWZDBHSGDKVAS'
    'COLORTERM' => 'truecolor'
    'COMMONPROGRAMFILES' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'EGOV-L-046'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CONFIG_SITE' => '/etc/config.site'
    'DISPLAY' => 'needs-to-be-defined'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_12604_1262719628' => '1'
    'EFC_12604_1592913036' => '1'
    'EFC_12604_2283032206' => '1'
    'EFC_12604_2775293581' => '1'
    'EFC_12604_3789132940' => '1'
    'EXEPATH' => 'C:\\Program Files\\Git\\bin'
    'FPS_BROWSER_APP_PROFILE_STRING' => 'Internet Explorer'
    'FPS_BROWSER_USER_PROFILE_STRING' => 'Default'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'HOME' => 'C:\\Users\\<USER>\\Users\\User'
    'HOSTNAME' => 'eGov-L-046'
    'IGCCSVC_DB' => 'AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg=='
    'INFOPATH' => '/mingw64/local/info:/mingw64/share/info:/usr/local/info:/usr/share/info:/usr/info:/share/info'
    'LANG' => 'en_US.UTF-8'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\EGOV-L-046'
    'MANPATH' => '/mingw64/local/man:/mingw64/share/man:/usr/local/man:/usr/share/man:/usr/man:/share/man'
    'MINGW_CHOST' => 'x86_64-w64-mingw32'
    'MINGW_PACKAGE_PREFIX' => 'mingw-w64-x86_64'
    'MINGW_PREFIX' => '/mingw64'
    'MSYSTEM' => 'MINGW64'
    'MSYSTEM_CARCH' => 'x86_64'
    'MSYSTEM_CHOST' => 'x86_64-w64-mingw32'
    'MSYSTEM_PREFIX' => '/mingw64'
    'NUMBER_OF_PROCESSORS' => '12'
    'OLDPWD' => '/c/Web/Reclassering/backend'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OneDriveCommercial' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OnlineServices' => 'Online Services'
    'ORIGINAL_PATH' => 'C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand'
    'ORIGINAL_TEMP' => '/tmp'
    'ORIGINAL_TMP' => '/tmp'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'PATH' => 'C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl;C:\\ProgramData\\ComposerSetup\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC'
    'PKG_CONFIG_PATH' => '/mingw64/lib/pkgconfig:/mingw64/share/pkgconfig'
    'PKG_CONFIG_SYSTEM_INCLUDE_PATH' => '/mingw64/include'
    'PKG_CONFIG_SYSTEM_LIBRARY_PATH' => '/mingw64/lib'
    'platformcode' => 'AN'
    'PLINK_PROTOCOL' => 'ssh'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 186 Stepping 3, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => 'ba03'
    'ProgramData' => 'C:\\ProgramData'
    'PROGRAMFILES' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PS1' => '\\[]633;A\\]\\[\\033]0;$TITLEPREFIX:$PWD\\007\\]\\n\\[\\033[32m\\]\\u@\\h \\[\\033[35m\\]$MSYSTEM \\[\\033[33m\\]\\w\\[\\033[36m\\]`__git_ps1`\\[\\033[0m\\]\\n$ \\[]633;B\\]'
    'PSModulePath' => 'C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe'
    'SHLVL' => '1'
    'SSH_ASKPASS' => '/mingw64/bin/git-askpass.exe'
    'SYSTEMDRIVE' => 'C:'
    'SYSTEMROOT' => 'C:\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.101.2'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMPDIR' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'EGOV-L-046'
    'USERDOMAIN_ROAMINGPROFILE' => 'EGOV-L-046'
    'USERNAME' => 'User'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Users\\User\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-34508b9eb2-sock'
    'WINDIR' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    '_' => '/usr/bin/winpty'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1751291808.4726
    'REQUEST_TIME' => 1751291808
    'argv' => [
        0 => 'yii'
        1 => 'migrate/create'
    ]
    'argc' => 2
]
2025-06-30 14:57:27 [-][-][-][error][yii\base\UnknownPropertyException] yii\base\UnknownPropertyException: Setting unknown property: bedezign\yii2\audit\Audit::enabled in C:\Web\Reclassering\vendor\yiisoft\yii2\base\Component.php:221
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\BaseYii.php(557): yii\base\Component->__set()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\base\BaseObject.php(107): yii\BaseYii::configure()
#2 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(161): yii\base\BaseObject->__construct()
#3 [internal function]: yii\base\Module->__construct()
#4 C:\Web\Reclassering\vendor\yiisoft\yii2\di\Container.php(419): ReflectionClass->newInstanceArgs()
#5 C:\Web\Reclassering\vendor\yiisoft\yii2\di\Container.php(170): yii\di\Container->build()
#6 C:\Web\Reclassering\vendor\yiisoft\yii2\BaseYii.php(365): yii\di\Container->get()
#7 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(445): yii\BaseYii::createObject()
#8 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(314): yii\base\Module->getModule()
#9 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(272): yii\base\Application->bootstrap()
#10 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(124): yii\base\Application->init()
#11 C:\Web\Reclassering\vendor\yiisoft\yii2\base\BaseObject.php(109): yii\console\Application->init()
#12 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(205): yii\base\BaseObject->__construct()
#13 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(89): yii\base\Application->__construct()
#14 C:\Web\Reclassering\yii(22): yii\console\Application->__construct()
#15 {main}
2025-06-30 14:57:27 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ACLOCAL_PATH' => '/mingw64/share/aclocal:/usr/share/aclocal'
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_2856_PIQMWZDBHSGDKVAS'
    'COLORTERM' => 'truecolor'
    'COMMONPROGRAMFILES' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'EGOV-L-046'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CONFIG_SITE' => '/etc/config.site'
    'DISPLAY' => 'needs-to-be-defined'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_12604_1262719628' => '1'
    'EFC_12604_1592913036' => '1'
    'EFC_12604_2283032206' => '1'
    'EFC_12604_2775293581' => '1'
    'EFC_12604_3789132940' => '1'
    'EXEPATH' => 'C:\\Program Files\\Git\\bin'
    'FPS_BROWSER_APP_PROFILE_STRING' => 'Internet Explorer'
    'FPS_BROWSER_USER_PROFILE_STRING' => 'Default'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'HOME' => 'C:\\Users\\<USER>\\Users\\User'
    'HOSTNAME' => 'eGov-L-046'
    'IGCCSVC_DB' => 'AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg=='
    'INFOPATH' => '/mingw64/local/info:/mingw64/share/info:/usr/local/info:/usr/share/info:/usr/info:/share/info'
    'LANG' => 'en_US.UTF-8'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\EGOV-L-046'
    'MANPATH' => '/mingw64/local/man:/mingw64/share/man:/usr/local/man:/usr/share/man:/usr/man:/share/man'
    'MINGW_CHOST' => 'x86_64-w64-mingw32'
    'MINGW_PACKAGE_PREFIX' => 'mingw-w64-x86_64'
    'MINGW_PREFIX' => '/mingw64'
    'MSYSTEM' => 'MINGW64'
    'MSYSTEM_CARCH' => 'x86_64'
    'MSYSTEM_CHOST' => 'x86_64-w64-mingw32'
    'MSYSTEM_PREFIX' => '/mingw64'
    'NUMBER_OF_PROCESSORS' => '12'
    'OLDPWD' => '/c/Web/Reclassering/backend'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OneDriveCommercial' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OnlineServices' => 'Online Services'
    'ORIGINAL_PATH' => 'C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand'
    'ORIGINAL_TEMP' => '/tmp'
    'ORIGINAL_TMP' => '/tmp'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'PATH' => 'C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl;C:\\ProgramData\\ComposerSetup\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC'
    'PKG_CONFIG_PATH' => '/mingw64/lib/pkgconfig:/mingw64/share/pkgconfig'
    'PKG_CONFIG_SYSTEM_INCLUDE_PATH' => '/mingw64/include'
    'PKG_CONFIG_SYSTEM_LIBRARY_PATH' => '/mingw64/lib'
    'platformcode' => 'AN'
    'PLINK_PROTOCOL' => 'ssh'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 186 Stepping 3, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => 'ba03'
    'ProgramData' => 'C:\\ProgramData'
    'PROGRAMFILES' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PS1' => '\\[]633;A\\]\\[\\033]0;$TITLEPREFIX:$PWD\\007\\]\\n\\[\\033[32m\\]\\u@\\h \\[\\033[35m\\]$MSYSTEM \\[\\033[33m\\]\\w\\[\\033[36m\\]`__git_ps1`\\[\\033[0m\\]\\n$ \\[]633;B\\]'
    'PSModulePath' => 'C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe'
    'SHLVL' => '1'
    'SSH_ASKPASS' => '/mingw64/bin/git-askpass.exe'
    'SYSTEMDRIVE' => 'C:'
    'SYSTEMROOT' => 'C:\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.101.2'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMPDIR' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'EGOV-L-046'
    'USERDOMAIN_ROAMINGPROFILE' => 'EGOV-L-046'
    'USERNAME' => 'User'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Users\\User\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-34508b9eb2-sock'
    'WINDIR' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    '_' => '/usr/bin/winpty'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1751291847.42
    'REQUEST_TIME' => 1751291847
    'argv' => [
        0 => 'yii'
        1 => 'migrate/create'
    ]
    'argc' => 2
]
2025-06-30 14:58:01 [-][-][-][error][yii\console\Exception] yii\console\Exception: Missing required arguments: name in C:\Web\Reclassering\vendor\yiisoft\yii2\console\Controller.php:256
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\base\InlineAction.php(51): yii\console\Controller->bindActionParams()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#2 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#3 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#4 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#5 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#6 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(385): yii\console\Application->handleRequest()
#7 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#8 {main}
2025-06-30 14:58:00 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ACLOCAL_PATH' => '/mingw64/share/aclocal:/usr/share/aclocal'
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_2856_PIQMWZDBHSGDKVAS'
    'COLORTERM' => 'truecolor'
    'COMMONPROGRAMFILES' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'EGOV-L-046'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CONFIG_SITE' => '/etc/config.site'
    'DISPLAY' => 'needs-to-be-defined'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_12604_1262719628' => '1'
    'EFC_12604_1592913036' => '1'
    'EFC_12604_2283032206' => '1'
    'EFC_12604_2775293581' => '1'
    'EFC_12604_3789132940' => '1'
    'EXEPATH' => 'C:\\Program Files\\Git\\bin'
    'FPS_BROWSER_APP_PROFILE_STRING' => 'Internet Explorer'
    'FPS_BROWSER_USER_PROFILE_STRING' => 'Default'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'HOME' => 'C:\\Users\\<USER>\\Users\\User'
    'HOSTNAME' => 'eGov-L-046'
    'IGCCSVC_DB' => 'AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg=='
    'INFOPATH' => '/mingw64/local/info:/mingw64/share/info:/usr/local/info:/usr/share/info:/usr/info:/share/info'
    'LANG' => 'en_US.UTF-8'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\EGOV-L-046'
    'MANPATH' => '/mingw64/local/man:/mingw64/share/man:/usr/local/man:/usr/share/man:/usr/man:/share/man'
    'MINGW_CHOST' => 'x86_64-w64-mingw32'
    'MINGW_PACKAGE_PREFIX' => 'mingw-w64-x86_64'
    'MINGW_PREFIX' => '/mingw64'
    'MSYSTEM' => 'MINGW64'
    'MSYSTEM_CARCH' => 'x86_64'
    'MSYSTEM_CHOST' => 'x86_64-w64-mingw32'
    'MSYSTEM_PREFIX' => '/mingw64'
    'NUMBER_OF_PROCESSORS' => '12'
    'OLDPWD' => '/c/Web/Reclassering/backend'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OneDriveCommercial' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OnlineServices' => 'Online Services'
    'ORIGINAL_PATH' => 'C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand'
    'ORIGINAL_TEMP' => '/tmp'
    'ORIGINAL_TMP' => '/tmp'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'PATH' => 'C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl;C:\\ProgramData\\ComposerSetup\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC'
    'PKG_CONFIG_PATH' => '/mingw64/lib/pkgconfig:/mingw64/share/pkgconfig'
    'PKG_CONFIG_SYSTEM_INCLUDE_PATH' => '/mingw64/include'
    'PKG_CONFIG_SYSTEM_LIBRARY_PATH' => '/mingw64/lib'
    'platformcode' => 'AN'
    'PLINK_PROTOCOL' => 'ssh'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 186 Stepping 3, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => 'ba03'
    'ProgramData' => 'C:\\ProgramData'
    'PROGRAMFILES' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PS1' => '\\[]633;A\\]\\[\\033]0;$TITLEPREFIX:$PWD\\007\\]\\n\\[\\033[32m\\]\\u@\\h \\[\\033[35m\\]$MSYSTEM \\[\\033[33m\\]\\w\\[\\033[36m\\]`__git_ps1`\\[\\033[0m\\]\\n$ \\[]633;B\\]'
    'PSModulePath' => 'C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe'
    'SHLVL' => '1'
    'SSH_ASKPASS' => '/mingw64/bin/git-askpass.exe'
    'SYSTEMDRIVE' => 'C:'
    'SYSTEMROOT' => 'C:\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.101.2'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMPDIR' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'EGOV-L-046'
    'USERDOMAIN_ROAMINGPROFILE' => 'EGOV-L-046'
    'USERNAME' => 'User'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Users\\User\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-34508b9eb2-sock'
    'WINDIR' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    '_' => '/usr/bin/winpty'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1751291880.7312
    'REQUEST_TIME' => 1751291880
    'argv' => [
        0 => 'yii'
        1 => 'migrate/create'
    ]
    'argc' => 2
]
2025-06-30 14:58:30 [-][-][-][error][yii\console\Exception] yii\console\Exception: The migration name should contain letters, digits, underscore and/or backslash characters only. in C:\Web\Reclassering\vendor\yiisoft\yii2\console\controllers\BaseMigrateController.php:652
Stack trace:
#0 [internal function]: yii\console\controllers\BaseMigrateController->actionCreate()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#2 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#3 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#4 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#5 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#6 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#7 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(385): yii\console\Application->handleRequest()
#8 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#9 {main}
2025-06-30 14:58:30 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ACLOCAL_PATH' => '/mingw64/share/aclocal:/usr/share/aclocal'
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_2856_PIQMWZDBHSGDKVAS'
    'COLORTERM' => 'truecolor'
    'COMMONPROGRAMFILES' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'EGOV-L-046'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CONFIG_SITE' => '/etc/config.site'
    'DISPLAY' => 'needs-to-be-defined'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_12604_1262719628' => '1'
    'EFC_12604_1592913036' => '1'
    'EFC_12604_2283032206' => '1'
    'EFC_12604_2775293581' => '1'
    'EFC_12604_3789132940' => '1'
    'EXEPATH' => 'C:\\Program Files\\Git\\bin'
    'FPS_BROWSER_APP_PROFILE_STRING' => 'Internet Explorer'
    'FPS_BROWSER_USER_PROFILE_STRING' => 'Default'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'HOME' => 'C:\\Users\\<USER>\\Users\\User'
    'HOSTNAME' => 'eGov-L-046'
    'IGCCSVC_DB' => 'AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg=='
    'INFOPATH' => '/mingw64/local/info:/mingw64/share/info:/usr/local/info:/usr/share/info:/usr/info:/share/info'
    'LANG' => 'en_US.UTF-8'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\EGOV-L-046'
    'MANPATH' => '/mingw64/local/man:/mingw64/share/man:/usr/local/man:/usr/share/man:/usr/man:/share/man'
    'MINGW_CHOST' => 'x86_64-w64-mingw32'
    'MINGW_PACKAGE_PREFIX' => 'mingw-w64-x86_64'
    'MINGW_PREFIX' => '/mingw64'
    'MSYSTEM' => 'MINGW64'
    'MSYSTEM_CARCH' => 'x86_64'
    'MSYSTEM_CHOST' => 'x86_64-w64-mingw32'
    'MSYSTEM_PREFIX' => '/mingw64'
    'NUMBER_OF_PROCESSORS' => '12'
    'OLDPWD' => '/c/Web/Reclassering/backend'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OneDriveCommercial' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OnlineServices' => 'Online Services'
    'ORIGINAL_PATH' => 'C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand'
    'ORIGINAL_TEMP' => '/tmp'
    'ORIGINAL_TMP' => '/tmp'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'PATH' => 'C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl;C:\\ProgramData\\ComposerSetup\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC'
    'PKG_CONFIG_PATH' => '/mingw64/lib/pkgconfig:/mingw64/share/pkgconfig'
    'PKG_CONFIG_SYSTEM_INCLUDE_PATH' => '/mingw64/include'
    'PKG_CONFIG_SYSTEM_LIBRARY_PATH' => '/mingw64/lib'
    'platformcode' => 'AN'
    'PLINK_PROTOCOL' => 'ssh'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 186 Stepping 3, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => 'ba03'
    'ProgramData' => 'C:\\ProgramData'
    'PROGRAMFILES' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PS1' => '\\[]633;A\\]\\[\\033]0;$TITLEPREFIX:$PWD\\007\\]\\n\\[\\033[32m\\]\\u@\\h \\[\\033[35m\\]$MSYSTEM \\[\\033[33m\\]\\w\\[\\033[36m\\]`__git_ps1`\\[\\033[0m\\]\\n$ \\[]633;B\\]'
    'PSModulePath' => 'C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe'
    'SHLVL' => '1'
    'SSH_ASKPASS' => '/mingw64/bin/git-askpass.exe'
    'SYSTEMDRIVE' => 'C:'
    'SYSTEMROOT' => 'C:\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.101.2'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMPDIR' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'EGOV-L-046'
    'USERDOMAIN_ROAMINGPROFILE' => 'EGOV-L-046'
    'USERNAME' => 'User'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Users\\User\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-34508b9eb2-sock'
    'WINDIR' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    '_' => '/usr/bin/winpty'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1751291910.8322
    'REQUEST_TIME' => 1751291910
    'argv' => [
        0 => 'yii'
        1 => 'migrate/create'
        2 => 'document-upload'
    ]
    'argc' => 3
]
2025-07-15 13:10:52 [-][-][-][warning][common\components\NotificationManager::trigger] No users found for roles: 2
2025-07-15 13:10:52 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ACLOCAL_PATH' => '/mingw64/share/aclocal:/usr/share/aclocal'
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_9588_WQQMVLMKOZYALKLS'
    'COLORTERM' => 'truecolor'
    'COMMONPROGRAMFILES' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'EGOV-L-046'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CONFIG_SITE' => '/etc/config.site'
    'DISPLAY' => 'needs-to-be-defined'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_10144_1262719628' => '1'
    'EFC_10144_1592913036' => '1'
    'EFC_10144_2283032206' => '1'
    'EFC_10144_2775293581' => '1'
    'EFC_10144_3789132940' => '1'
    'EXEPATH' => 'C:\\Program Files\\Git\\bin'
    'FPS_BROWSER_APP_PROFILE_STRING' => 'Internet Explorer'
    'FPS_BROWSER_USER_PROFILE_STRING' => 'Default'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'GIT_PAGER' => 'cat'
    'HOME' => 'C:\\Users\\<USER>\\Users\\User'
    'HOSTNAME' => 'eGov-L-046'
    'IGCCSVC_DB' => 'AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg=='
    'INFOPATH' => '/mingw64/local/info:/mingw64/share/info:/usr/local/info:/usr/share/info:/usr/info:/share/info'
    'LANG' => 'en_US.UTF-8'
    'LESS' => '-FX'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\EGOV-L-046'
    'MANPATH' => '/mingw64/local/man:/mingw64/share/man:/usr/local/man:/usr/share/man:/usr/man:/share/man'
    'MINGW_CHOST' => 'x86_64-w64-mingw32'
    'MINGW_PACKAGE_PREFIX' => 'mingw-w64-x86_64'
    'MINGW_PREFIX' => '/mingw64'
    'MSYSTEM' => 'MINGW64'
    'MSYSTEM_CARCH' => 'x86_64'
    'MSYSTEM_CHOST' => 'x86_64-w64-mingw32'
    'MSYSTEM_PREFIX' => '/mingw64'
    'NUMBER_OF_PROCESSORS' => '12'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OneDriveCommercial' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OnlineServices' => 'Online Services'
    'ORIGINAL_PATH' => 'C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files (x86)\\HP\\HP OCR\\DB_Lib;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand'
    'ORIGINAL_TEMP' => '/tmp'
    'ORIGINAL_TMP' => '/tmp'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'PAGER' => 'cat'
    'PATH' => 'C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files (x86)\\HP\\HP OCR\\DB_Lib;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl;C:\\ProgramData\\ComposerSetup\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC'
    'PKG_CONFIG_PATH' => '/mingw64/lib/pkgconfig:/mingw64/share/pkgconfig'
    'PKG_CONFIG_SYSTEM_INCLUDE_PATH' => '/mingw64/include'
    'PKG_CONFIG_SYSTEM_LIBRARY_PATH' => '/mingw64/lib'
    'platformcode' => 'AN'
    'PLINK_PROTOCOL' => 'ssh'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 186 Stepping 3, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => 'ba03'
    'ProgramData' => 'C:\\ProgramData'
    'PROGRAMFILES' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PS1' => '\\[]633;A\\]\\[\\033]0;$TITLEPREFIX:$PWD\\007\\]\\n\\[\\033[32m\\]\\u@\\h \\[\\033[35m\\]$MSYSTEM \\[\\033[33m\\]\\w\\[\\033[36m\\]`__git_ps1`\\[\\033[0m\\]\\n$ \\[]633;B\\]'
    'PSModulePath' => 'C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe'
    'SHLVL' => '1'
    'SSH_ASKPASS' => '/mingw64/bin/git-askpass.exe'
    'SYSTEMDRIVE' => 'C:'
    'SYSTEMROOT' => 'C:\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.102.0'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMPDIR' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'EGOV-L-046'
    'USERDOMAIN_ROAMINGPROFILE' => 'EGOV-L-046'
    'USERNAME' => 'User'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Users\\User\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-34508b9eb2-sock'
    'WINDIR' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    '_' => '/usr/bin/winpty'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1752581452.8389
    'REQUEST_TIME' => 1752581452
    'argv' => [
        0 => 'yii'
        1 => 'notification/test'
        2 => 'document_created'
    ]
    'argc' => 3
]
2025-07-22 16:14:38 [-][-][-][error][yii\base\UnknownPropertyException] yii\base\UnknownPropertyException: Getting unknown property: yii\console\Application::user in C:\Web\Reclassering\vendor\yiisoft\yii2\base\Component.php:154
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\di\ServiceLocator.php(77): yii\base\Component->__get()
#1 C:\Web\Reclassering\common\models\RoleFunctionality.php(53): yii\di\ServiceLocator->__get()
#2 [internal function]: common\models\RoleFunctionality->common\models\{closure}()
#3 C:\Web\Reclassering\vendor\yiisoft\yii2\behaviors\AttributeBehavior.php(145): call_user_func()
#4 C:\Web\Reclassering\vendor\yiisoft\yii2\behaviors\BlameableBehavior.php(115): yii\behaviors\AttributeBehavior->getValue()
#5 C:\Web\Reclassering\vendor\yiisoft\yii2\behaviors\AttributeBehavior.php(122): yii\behaviors\BlameableBehavior->getValue()
#6 [internal function]: yii\behaviors\AttributeBehavior->evaluateAttributes()
#7 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Component.php(648): call_user_func()
#8 C:\Web\Reclassering\vendor\yiisoft\yii2\db\BaseActiveRecord.php(984): yii\base\Component->trigger()
#9 C:\Web\Reclassering\vendor\yiisoft\yii2\db\ActiveRecord.php(606): yii\db\BaseActiveRecord->beforeSave()
#10 C:\Web\Reclassering\vendor\yiisoft\yii2\db\ActiveRecord.php(576): yii\db\ActiveRecord->insertInternal()
#11 C:\Web\Reclassering\vendor\yiisoft\yii2\db\BaseActiveRecord.php(688): yii\db\ActiveRecord->insert()
#12 C:\Web\Reclassering\console\controllers\AccessController.php(124): yii\db\BaseActiveRecord->save()
#13 [internal function]: console\controllers\AccessController->actionAdd()
#14 C:\Web\Reclassering\vendor\yiisoft\yii2\base\InlineAction.php(60): call_user_func_array()
#15 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Controller.php(184): yii\base\InlineAction->runWithParams()
#16 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#17 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(555): yii\console\Controller->runAction()
#18 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#19 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#20 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(387): yii\console\Application->handleRequest()
#21 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#22 {main}
2025-07-22 16:14:38 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ACLOCAL_PATH' => '/mingw64/share/aclocal:/usr/share/aclocal'
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_25756_NGODOWYADWGWCCJF'
    'COLORTERM' => 'truecolor'
    'COMMONPROGRAMFILES' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'EGOV-L-046'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CONFIG_SITE' => '/etc/config.site'
    'DISPLAY' => 'needs-to-be-defined'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_8428_1592913036' => '1'
    'EXEPATH' => 'C:\\Program Files\\Git\\bin'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'GIT_PAGER' => 'cat'
    'HOME' => 'C:\\Users\\<USER>\\Users\\User'
    'HOSTNAME' => 'eGov-L-046'
    'IGCCSVC_DB' => 'AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg=='
    'INFOPATH' => '/mingw64/local/info:/mingw64/share/info:/usr/local/info:/usr/share/info:/usr/info:/share/info'
    'LANG' => 'en_US.UTF-8'
    'LESS' => '-FX'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\EGOV-L-046'
    'MANPATH' => '/mingw64/local/man:/mingw64/share/man:/usr/local/man:/usr/share/man:/usr/man:/share/man'
    'MINGW_CHOST' => 'x86_64-w64-mingw32'
    'MINGW_PACKAGE_PREFIX' => 'mingw-w64-x86_64'
    'MINGW_PREFIX' => '/mingw64'
    'MSYSTEM' => 'MINGW64'
    'MSYSTEM_CARCH' => 'x86_64'
    'MSYSTEM_CHOST' => 'x86_64-w64-mingw32'
    'MSYSTEM_PREFIX' => '/mingw64'
    'NUMBER_OF_PROCESSORS' => '12'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OneDriveCommercial' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OnlineServices' => 'Online Services'
    'ORIGINAL_PATH' => 'C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files (x86)\\HP\\HP OCR\\DB_Lib;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand'
    'ORIGINAL_TEMP' => '/tmp'
    'ORIGINAL_TMP' => '/tmp'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'PAGER' => 'cat'
    'PATH' => 'C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files (x86)\\HP\\HP OCR\\DB_Lib;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl;C:\\ProgramData\\ComposerSetup\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC'
    'PKG_CONFIG_PATH' => '/mingw64/lib/pkgconfig:/mingw64/share/pkgconfig'
    'PKG_CONFIG_SYSTEM_INCLUDE_PATH' => '/mingw64/include'
    'PKG_CONFIG_SYSTEM_LIBRARY_PATH' => '/mingw64/lib'
    'platformcode' => 'AN'
    'PLINK_PROTOCOL' => 'ssh'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 186 Stepping 3, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => 'ba03'
    'ProgramData' => 'C:\\ProgramData'
    'PROGRAMFILES' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PS1' => '\\[]633;A\\]\\[\\033]0;$TITLEPREFIX:$PWD\\007\\]\\n\\[\\033[32m\\]\\u@\\h \\[\\033[35m\\]$MSYSTEM \\[\\033[33m\\]\\w\\[\\033[36m\\]`__git_ps1`\\[\\033[0m\\]\\n$ \\[]633;B\\]'
    'PSModulePath' => 'C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe'
    'SHLVL' => '1'
    'SSH_ASKPASS' => '/mingw64/bin/git-askpass.exe'
    'SYSTEMDRIVE' => 'C:'
    'SYSTEMROOT' => 'C:\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.102.1'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMPDIR' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'EGOV-L-046'
    'USERDOMAIN_ROAMINGPROFILE' => 'EGOV-L-046'
    'USERNAME' => 'User'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Users\\User\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-34508b9eb2-sock'
    'WINDIR' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    '_' => '/usr/bin/winpty'
    '__COMPAT_LAYER' => 'DetectorsAppHealth'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => **********.5801
    'REQUEST_TIME' => **********
    'argv' => [
        0 => 'yii'
        1 => 'access/add'
        2 => '4'
        3 => 'C:/Program Files/Git/site/index'
    ]
    'argc' => 4
]
2025-07-22 16:15:11 [-][-][-][error][yii\base\UnknownPropertyException] yii\base\UnknownPropertyException: Getting unknown property: common\models\RoleFunctionality::user_id in C:\Web\Reclassering\vendor\yiisoft\yii2\base\Component.php:154
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\db\BaseActiveRecord.php(296): yii\base\Component->__get()
#1 C:\Web\Reclassering\common\models\RoleFunctionality.php(138): yii\db\BaseActiveRecord->__get()
#2 C:\Web\Reclassering\vendor\yiisoft\yii2\db\ActiveRecord.php(621): common\models\RoleFunctionality->afterSave()
#3 C:\Web\Reclassering\vendor\yiisoft\yii2\db\ActiveRecord.php(576): yii\db\ActiveRecord->insertInternal()
#4 C:\Web\Reclassering\vendor\yiisoft\yii2\db\BaseActiveRecord.php(688): yii\db\ActiveRecord->insert()
#5 C:\Web\Reclassering\console\controllers\AccessController.php(124): yii\db\BaseActiveRecord->save()
#6 [internal function]: console\controllers\AccessController->actionAdd()
#7 C:\Web\Reclassering\vendor\yiisoft\yii2\base\InlineAction.php(60): call_user_func_array()
#8 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Controller.php(184): yii\base\InlineAction->runWithParams()
#9 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#10 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(555): yii\console\Controller->runAction()
#11 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#12 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#13 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(387): yii\console\Application->handleRequest()
#14 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#15 {main}
2025-07-22 16:15:11 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ACLOCAL_PATH' => '/mingw64/share/aclocal:/usr/share/aclocal'
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_25756_NGODOWYADWGWCCJF'
    'COLORTERM' => 'truecolor'
    'COMMONPROGRAMFILES' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'EGOV-L-046'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CONFIG_SITE' => '/etc/config.site'
    'DISPLAY' => 'needs-to-be-defined'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_8428_1592913036' => '1'
    'EXEPATH' => 'C:\\Program Files\\Git\\bin'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'GIT_PAGER' => 'cat'
    'HOME' => 'C:\\Users\\<USER>\\Users\\User'
    'HOSTNAME' => 'eGov-L-046'
    'IGCCSVC_DB' => 'AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg=='
    'INFOPATH' => '/mingw64/local/info:/mingw64/share/info:/usr/local/info:/usr/share/info:/usr/info:/share/info'
    'LANG' => 'en_US.UTF-8'
    'LESS' => '-FX'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\EGOV-L-046'
    'MANPATH' => '/mingw64/local/man:/mingw64/share/man:/usr/local/man:/usr/share/man:/usr/man:/share/man'
    'MINGW_CHOST' => 'x86_64-w64-mingw32'
    'MINGW_PACKAGE_PREFIX' => 'mingw-w64-x86_64'
    'MINGW_PREFIX' => '/mingw64'
    'MSYSTEM' => 'MINGW64'
    'MSYSTEM_CARCH' => 'x86_64'
    'MSYSTEM_CHOST' => 'x86_64-w64-mingw32'
    'MSYSTEM_PREFIX' => '/mingw64'
    'NUMBER_OF_PROCESSORS' => '12'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OneDriveCommercial' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OnlineServices' => 'Online Services'
    'ORIGINAL_PATH' => 'C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files (x86)\\HP\\HP OCR\\DB_Lib;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand'
    'ORIGINAL_TEMP' => '/tmp'
    'ORIGINAL_TMP' => '/tmp'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'PAGER' => 'cat'
    'PATH' => 'C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files (x86)\\HP\\HP OCR\\DB_Lib;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl;C:\\ProgramData\\ComposerSetup\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC'
    'PKG_CONFIG_PATH' => '/mingw64/lib/pkgconfig:/mingw64/share/pkgconfig'
    'PKG_CONFIG_SYSTEM_INCLUDE_PATH' => '/mingw64/include'
    'PKG_CONFIG_SYSTEM_LIBRARY_PATH' => '/mingw64/lib'
    'platformcode' => 'AN'
    'PLINK_PROTOCOL' => 'ssh'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 186 Stepping 3, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => 'ba03'
    'ProgramData' => 'C:\\ProgramData'
    'PROGRAMFILES' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PS1' => '\\[]633;A\\]\\[\\033]0;$TITLEPREFIX:$PWD\\007\\]\\n\\[\\033[32m\\]\\u@\\h \\[\\033[35m\\]$MSYSTEM \\[\\033[33m\\]\\w\\[\\033[36m\\]`__git_ps1`\\[\\033[0m\\]\\n$ \\[]633;B\\]'
    'PSModulePath' => 'C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe'
    'SHLVL' => '1'
    'SSH_ASKPASS' => '/mingw64/bin/git-askpass.exe'
    'SYSTEMDRIVE' => 'C:'
    'SYSTEMROOT' => 'C:\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.102.1'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMPDIR' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'EGOV-L-046'
    'USERDOMAIN_ROAMINGPROFILE' => 'EGOV-L-046'
    'USERNAME' => 'User'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Users\\User\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-34508b9eb2-sock'
    'WINDIR' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    '_' => '/usr/bin/winpty'
    '__COMPAT_LAYER' => 'DetectorsAppHealth'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => **********.4928
    'REQUEST_TIME' => **********
    'argv' => [
        0 => 'yii'
        1 => 'access/add'
        2 => '4'
        3 => 'C:/Program Files/Git/site/index'
    ]
    'argc' => 4
]
2025-07-22 16:42:41 [-][-][-][error][yii\console\UnknownCommandException] yii\base\InvalidRouteException: Unable to resolve the request: access/clear-cacje in C:\Web\Reclassering\vendor\yiisoft\yii2\base\Controller.php:155
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(555): yii\console\Controller->runAction()
#2 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#3 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#4 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(387): yii\console\Application->handleRequest()
#5 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#6 {main}

Next yii\console\UnknownCommandException: Unknown command "access/clear-cacje". in C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php:183
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(387): yii\console\Application->handleRequest()
#2 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#3 {main}
2025-07-22 16:42:41 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ACLOCAL_PATH' => '/mingw64/share/aclocal:/usr/share/aclocal'
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_25756_NGODOWYADWGWCCJF'
    'COLORTERM' => 'truecolor'
    'COMMONPROGRAMFILES' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'EGOV-L-046'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CONFIG_SITE' => '/etc/config.site'
    'DISPLAY' => 'needs-to-be-defined'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_8428_1592913036' => '1'
    'EXEPATH' => 'C:\\Program Files\\Git\\bin'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'GIT_PAGER' => 'cat'
    'HOME' => 'C:\\Users\\<USER>\\Users\\User'
    'HOSTNAME' => 'eGov-L-046'
    'IGCCSVC_DB' => 'AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg=='
    'INFOPATH' => '/mingw64/local/info:/mingw64/share/info:/usr/local/info:/usr/share/info:/usr/info:/share/info'
    'LANG' => 'en_US.UTF-8'
    'LESS' => '-FX'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\EGOV-L-046'
    'MANPATH' => '/mingw64/local/man:/mingw64/share/man:/usr/local/man:/usr/share/man:/usr/man:/share/man'
    'MINGW_CHOST' => 'x86_64-w64-mingw32'
    'MINGW_PACKAGE_PREFIX' => 'mingw-w64-x86_64'
    'MINGW_PREFIX' => '/mingw64'
    'MSYSTEM' => 'MINGW64'
    'MSYSTEM_CARCH' => 'x86_64'
    'MSYSTEM_CHOST' => 'x86_64-w64-mingw32'
    'MSYSTEM_PREFIX' => '/mingw64'
    'NUMBER_OF_PROCESSORS' => '12'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OneDriveCommercial' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OnlineServices' => 'Online Services'
    'ORIGINAL_PATH' => 'C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files (x86)\\HP\\HP OCR\\DB_Lib;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand'
    'ORIGINAL_TEMP' => '/tmp'
    'ORIGINAL_TMP' => '/tmp'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'PAGER' => 'cat'
    'PATH' => 'C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files (x86)\\HP\\HP OCR\\DB_Lib;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl;C:\\ProgramData\\ComposerSetup\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC'
    'PKG_CONFIG_PATH' => '/mingw64/lib/pkgconfig:/mingw64/share/pkgconfig'
    'PKG_CONFIG_SYSTEM_INCLUDE_PATH' => '/mingw64/include'
    'PKG_CONFIG_SYSTEM_LIBRARY_PATH' => '/mingw64/lib'
    'platformcode' => 'AN'
    'PLINK_PROTOCOL' => 'ssh'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 186 Stepping 3, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => 'ba03'
    'ProgramData' => 'C:\\ProgramData'
    'PROGRAMFILES' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PS1' => '\\[]633;A\\]\\[\\033]0;$TITLEPREFIX:$PWD\\007\\]\\n\\[\\033[32m\\]\\u@\\h \\[\\033[35m\\]$MSYSTEM \\[\\033[33m\\]\\w\\[\\033[36m\\]`__git_ps1`\\[\\033[0m\\]\\n$ \\[]633;B\\]'
    'PSModulePath' => 'C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe'
    'SHLVL' => '1'
    'SSH_ASKPASS' => '/mingw64/bin/git-askpass.exe'
    'SYSTEMDRIVE' => 'C:'
    'SYSTEMROOT' => 'C:\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.102.1'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMPDIR' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'EGOV-L-046'
    'USERDOMAIN_ROAMINGPROFILE' => 'EGOV-L-046'
    'USERNAME' => 'User'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Users\\User\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-34508b9eb2-sock'
    'WINDIR' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    '_' => '/usr/bin/winpty'
    '__COMPAT_LAYER' => 'DetectorsAppHealth'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => **********.8056
    'REQUEST_TIME' => **********
    'argv' => [
        0 => 'yii'
        1 => 'access/clear-cacje'
    ]
    'argc' => 2
]
2025-07-22 12:49:20 [-][-][-][error][Error] Error: Class "backend\controllers\RoleFunctionalityController" not found in C:\Web\Reclassering\test_cache_clearing.php:26
Stack trace:
#0 {main}
2025-07-22 12:49:20 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ACLOCAL_PATH' => '/mingw64/share/aclocal:/usr/share/aclocal'
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_25756_NGODOWYADWGWCCJF'
    'COLORTERM' => 'truecolor'
    'COMMONPROGRAMFILES' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'EGOV-L-046'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CONFIG_SITE' => '/etc/config.site'
    'DISPLAY' => 'needs-to-be-defined'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_8428_1592913036' => '1'
    'EXEPATH' => 'C:\\Program Files\\Git\\bin'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'GIT_PAGER' => 'cat'
    'HOME' => 'C:\\Users\\<USER>\\Users\\User'
    'HOSTNAME' => 'eGov-L-046'
    'IGCCSVC_DB' => 'AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg=='
    'INFOPATH' => '/mingw64/local/info:/mingw64/share/info:/usr/local/info:/usr/share/info:/usr/info:/share/info'
    'LANG' => 'en_US.UTF-8'
    'LESS' => '-FX'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\EGOV-L-046'
    'MANPATH' => '/mingw64/local/man:/mingw64/share/man:/usr/local/man:/usr/share/man:/usr/man:/share/man'
    'MINGW_CHOST' => 'x86_64-w64-mingw32'
    'MINGW_PACKAGE_PREFIX' => 'mingw-w64-x86_64'
    'MINGW_PREFIX' => '/mingw64'
    'MSYSTEM' => 'MINGW64'
    'MSYSTEM_CARCH' => 'x86_64'
    'MSYSTEM_CHOST' => 'x86_64-w64-mingw32'
    'MSYSTEM_PREFIX' => '/mingw64'
    'NUMBER_OF_PROCESSORS' => '12'
    'OLDPWD' => '/c/Web/Reclassering/backend'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OneDriveCommercial' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OnlineServices' => 'Online Services'
    'ORIGINAL_PATH' => 'C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files (x86)\\HP\\HP OCR\\DB_Lib;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand'
    'ORIGINAL_TEMP' => '/tmp'
    'ORIGINAL_TMP' => '/tmp'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'PAGER' => 'cat'
    'PATH' => 'C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files (x86)\\HP\\HP OCR\\DB_Lib;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl;C:\\ProgramData\\ComposerSetup\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC'
    'PKG_CONFIG_PATH' => '/mingw64/lib/pkgconfig:/mingw64/share/pkgconfig'
    'PKG_CONFIG_SYSTEM_INCLUDE_PATH' => '/mingw64/include'
    'PKG_CONFIG_SYSTEM_LIBRARY_PATH' => '/mingw64/lib'
    'platformcode' => 'AN'
    'PLINK_PROTOCOL' => 'ssh'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 186 Stepping 3, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => 'ba03'
    'ProgramData' => 'C:\\ProgramData'
    'PROGRAMFILES' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PS1' => '\\[]633;A\\]\\[\\033]0;$TITLEPREFIX:$PWD\\007\\]\\n\\[\\033[32m\\]\\u@\\h \\[\\033[35m\\]$MSYSTEM \\[\\033[33m\\]\\w\\[\\033[36m\\]`__git_ps1`\\[\\033[0m\\]\\n$ \\[]633;B\\]'
    'PSModulePath' => 'C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe'
    'SHLVL' => '1'
    'SSH_ASKPASS' => '/mingw64/bin/git-askpass.exe'
    'SYSTEMDRIVE' => 'C:'
    'SYSTEMROOT' => 'C:\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.102.1'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMPDIR' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'EGOV-L-046'
    'USERDOMAIN_ROAMINGPROFILE' => 'EGOV-L-046'
    'USERNAME' => 'User'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Users\\User\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-34508b9eb2-sock'
    'WINDIR' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    '_' => '/usr/bin/winpty'
    '__COMPAT_LAYER' => 'DetectorsAppHealth'
    'PHP_SELF' => 'test_cache_clearing.php'
    'SCRIPT_NAME' => 'test_cache_clearing.php'
    'SCRIPT_FILENAME' => 'test_cache_clearing.php'
    'PATH_TRANSLATED' => 'test_cache_clearing.php'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => **********.5786
    'REQUEST_TIME' => **********
    'argv' => [
        0 => 'test_cache_clearing.php'
    ]
    'argc' => 1
]
2025-08-18 16:59:16 [-][-][-][error][yii\base\UnknownClassException] yii\base\UnknownClassException: Unable to find 'console\controllers\PermissionController' in file: C:\Web\Reclassering/console/controllers/PermissionController.php. Namespace missing? in C:\Web\Reclassering\vendor\yiisoft\yii2\BaseYii.php:299
Stack trace:
#0 [internal function]: yii\BaseYii::autoload()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(664): class_exists()
#2 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(623): yii\base\Module->createControllerByID()
#3 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(549): yii\base\Module->createController()
#4 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#5 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#6 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(387): yii\console\Application->handleRequest()
#7 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#8 {main}
2025-08-18 16:59:16 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ACLOCAL_PATH' => '/mingw64/share/aclocal:/usr/share/aclocal'
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'ChocolateyInstall' => 'C:\\ProgramData\\chocolatey'
    'ChocolateyLastPathUpdate' => '133976692032696195'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_11428_QNLKECASYTIFLKFY'
    'COLORTERM' => 'truecolor'
    'COMMONPROGRAMFILES' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'EGOV-L-046'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CONFIG_SITE' => '/etc/config.site'
    'DISPLAY' => 'needs-to-be-defined'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_23924_1262719628' => '1'
    'EFC_23924_1592913036' => '1'
    'EFC_23924_2283032206' => '1'
    'EFC_23924_2775293581' => '1'
    'EFC_23924_3789132940' => '1'
    'EXEPATH' => 'C:\\Program Files\\Git\\bin'
    'FPS_BROWSER_APP_PROFILE_STRING' => 'Internet Explorer'
    'FPS_BROWSER_USER_PROFILE_STRING' => 'Default'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'HOME' => 'C:\\Users\\<USER>\\Users\\User'
    'HOSTNAME' => 'eGov-L-046'
    'IGCCSVC_DB' => 'AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg=='
    'INFOPATH' => '/mingw64/local/info:/mingw64/share/info:/usr/local/info:/usr/share/info:/usr/info:/share/info'
    'LANG' => 'en_US.UTF-8'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\EGOV-L-046'
    'MANPATH' => '/mingw64/local/man:/mingw64/share/man:/usr/local/man:/usr/share/man:/usr/man:/share/man'
    'MINGW_CHOST' => 'x86_64-w64-mingw32'
    'MINGW_PACKAGE_PREFIX' => 'mingw-w64-x86_64'
    'MINGW_PREFIX' => '/mingw64'
    'MSYSTEM' => 'MINGW64'
    'MSYSTEM_CARCH' => 'x86_64'
    'MSYSTEM_CHOST' => 'x86_64-w64-mingw32'
    'MSYSTEM_PREFIX' => '/mingw64'
    'NUMBER_OF_PROCESSORS' => '12'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OneDriveCommercial' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OnlineServices' => 'Online Services'
    'ORIGINAL_PATH' => 'C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files (x86)\\HP\\HP OCR\\DB_Lib;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand'
    'ORIGINAL_TEMP' => '/tmp'
    'ORIGINAL_TMP' => '/tmp'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'PATH' => 'C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files (x86)\\HP\\HP OCR\\DB_Lib;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl;C:\\ProgramData\\ComposerSetup\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW'
    'PKG_CONFIG_PATH' => '/mingw64/lib/pkgconfig:/mingw64/share/pkgconfig'
    'PKG_CONFIG_SYSTEM_INCLUDE_PATH' => '/mingw64/include'
    'PKG_CONFIG_SYSTEM_LIBRARY_PATH' => '/mingw64/lib'
    'platformcode' => 'AN'
    'PLINK_PROTOCOL' => 'ssh'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 186 Stepping 3, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => 'ba03'
    'ProgramData' => 'C:\\ProgramData'
    'PROGRAMFILES' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PS1' => '\\[]633;A\\]\\[\\033]0;$TITLEPREFIX:$PWD\\007\\]\\n\\[\\033[32m\\]\\u@\\h \\[\\033[35m\\]$MSYSTEM \\[\\033[33m\\]\\w\\[\\033[36m\\]`__git_ps1`\\[\\033[0m\\]\\n$ \\[]633;B\\]'
    'PSModulePath' => 'C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe'
    'SHLVL' => '1'
    'SSH_ASKPASS' => '/mingw64/bin/git-askpass.exe'
    'SYSTEMDRIVE' => 'C:'
    'SYSTEMROOT' => 'C:\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.103.0'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMPDIR' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'EGOV-L-046'
    'USERDOMAIN_ROAMINGPROFILE' => 'EGOV-L-046'
    'USERNAME' => 'User'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Users\\User\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-34508b9eb2-sock'
    'WINDIR' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    '_' => '/usr/bin/winpty'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1755532756.2672
    'REQUEST_TIME' => 1755532756
    'argv' => [
        0 => 'yii'
        1 => 'permission/check'
        2 => '4'
    ]
    'argc' => 3
]
2025-08-18 17:30:22 [-][-][-][error][yii\console\Exception] yii\console\Exception: Missing required arguments: userId in C:\Web\Reclassering\vendor\yiisoft\yii2\console\Controller.php:256
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\base\InlineAction.php(54): yii\console\Controller->bindActionParams()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Controller.php(184): yii\base\InlineAction->runWithParams()
#2 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#3 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(555): yii\console\Controller->runAction()
#4 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#5 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#6 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(387): yii\console\Application->handleRequest()
#7 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#8 {main}
2025-08-18 17:30:22 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ACLOCAL_PATH' => '/mingw64/share/aclocal:/usr/share/aclocal'
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'ChocolateyInstall' => 'C:\\ProgramData\\chocolatey'
    'ChocolateyLastPathUpdate' => '133976692032696195'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_11428_QNLKECASYTIFLKFY'
    'COLORTERM' => 'truecolor'
    'COMMONPROGRAMFILES' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'EGOV-L-046'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CONFIG_SITE' => '/etc/config.site'
    'DISPLAY' => 'needs-to-be-defined'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_23924_1262719628' => '1'
    'EFC_23924_1592913036' => '1'
    'EFC_23924_2283032206' => '1'
    'EFC_23924_2775293581' => '1'
    'EFC_23924_3789132940' => '1'
    'EXEPATH' => 'C:\\Program Files\\Git\\bin'
    'FPS_BROWSER_APP_PROFILE_STRING' => 'Internet Explorer'
    'FPS_BROWSER_USER_PROFILE_STRING' => 'Default'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'HOME' => 'C:\\Users\\<USER>\\Users\\User'
    'HOSTNAME' => 'eGov-L-046'
    'IGCCSVC_DB' => 'AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg=='
    'INFOPATH' => '/mingw64/local/info:/mingw64/share/info:/usr/local/info:/usr/share/info:/usr/info:/share/info'
    'LANG' => 'en_US.UTF-8'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\EGOV-L-046'
    'MANPATH' => '/mingw64/local/man:/mingw64/share/man:/usr/local/man:/usr/share/man:/usr/man:/share/man'
    'MINGW_CHOST' => 'x86_64-w64-mingw32'
    'MINGW_PACKAGE_PREFIX' => 'mingw-w64-x86_64'
    'MINGW_PREFIX' => '/mingw64'
    'MSYSTEM' => 'MINGW64'
    'MSYSTEM_CARCH' => 'x86_64'
    'MSYSTEM_CHOST' => 'x86_64-w64-mingw32'
    'MSYSTEM_PREFIX' => '/mingw64'
    'NUMBER_OF_PROCESSORS' => '12'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OneDriveCommercial' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OnlineServices' => 'Online Services'
    'ORIGINAL_PATH' => 'C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files (x86)\\HP\\HP OCR\\DB_Lib;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand'
    'ORIGINAL_TEMP' => '/tmp'
    'ORIGINAL_TMP' => '/tmp'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'PATH' => 'C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files (x86)\\HP\\HP OCR\\DB_Lib;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl;C:\\ProgramData\\ComposerSetup\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW'
    'PKG_CONFIG_PATH' => '/mingw64/lib/pkgconfig:/mingw64/share/pkgconfig'
    'PKG_CONFIG_SYSTEM_INCLUDE_PATH' => '/mingw64/include'
    'PKG_CONFIG_SYSTEM_LIBRARY_PATH' => '/mingw64/lib'
    'platformcode' => 'AN'
    'PLINK_PROTOCOL' => 'ssh'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 186 Stepping 3, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => 'ba03'
    'ProgramData' => 'C:\\ProgramData'
    'PROGRAMFILES' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PS1' => '\\[]633;A\\]\\[\\033]0;$TITLEPREFIX:$PWD\\007\\]\\n\\[\\033[32m\\]\\u@\\h \\[\\033[35m\\]$MSYSTEM \\[\\033[33m\\]\\w\\[\\033[36m\\]`__git_ps1`\\[\\033[0m\\]\\n$ \\[]633;B\\]'
    'PSModulePath' => 'C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe'
    'SHLVL' => '1'
    'SSH_ASKPASS' => '/mingw64/bin/git-askpass.exe'
    'SYSTEMDRIVE' => 'C:'
    'SYSTEMROOT' => 'C:\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.103.0'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMPDIR' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'EGOV-L-046'
    'USERDOMAIN_ROAMINGPROFILE' => 'EGOV-L-046'
    'USERNAME' => 'User'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Users\\User\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-34508b9eb2-sock'
    'WINDIR' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    '_' => '/usr/bin/winpty'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1755534622.0865
    'REQUEST_TIME' => 1755534622
    'argv' => [
        0 => 'yii'
        1 => 'permission-cache/show'
    ]
    'argc' => 2
]
2025-08-20 16:56:27 [-][-][-][error][yii\console\UnknownCommandException] yii\base\InvalidRouteException: Unable to resolve the request: permission-cache/check in C:\Web\Reclassering\vendor\yiisoft\yii2\base\Controller.php:155
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(555): yii\console\Controller->runAction()
#2 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#3 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#4 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(387): yii\console\Application->handleRequest()
#5 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#6 {main}

Next yii\console\UnknownCommandException: Unknown command "permission-cache/check". in C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php:183
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(387): yii\console\Application->handleRequest()
#2 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#3 {main}
2025-08-20 16:56:27 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ACLOCAL_PATH' => '/mingw64/share/aclocal:/usr/share/aclocal'
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'ChocolateyInstall' => 'C:\\ProgramData\\chocolatey'
    'ChocolateyLastPathUpdate' => '133976692032696195'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_17836_YGHAVHNBSJCMXJNH'
    'COLORTERM' => 'truecolor'
    'COMMONPROGRAMFILES' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'EGOV-L-046'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CONFIG_SITE' => '/etc/config.site'
    'DISPLAY' => 'needs-to-be-defined'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_26664_1262719628' => '1'
    'EFC_26664_1592913036' => '1'
    'EFC_26664_2283032206' => '1'
    'EFC_26664_2775293581' => '1'
    'EFC_26664_3789132940' => '1'
    'EXEPATH' => 'C:\\Program Files\\Git\\bin'
    'FPS_BROWSER_APP_PROFILE_STRING' => 'Internet Explorer'
    'FPS_BROWSER_USER_PROFILE_STRING' => 'Default'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'HOME' => 'C:\\Users\\<USER>\\Users\\User'
    'HOSTNAME' => 'eGov-L-046'
    'IGCCSVC_DB' => 'AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg=='
    'INFOPATH' => '/mingw64/local/info:/mingw64/share/info:/usr/local/info:/usr/share/info:/usr/info:/share/info'
    'LANG' => 'en_US.UTF-8'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\EGOV-L-046'
    'MANPATH' => '/mingw64/local/man:/mingw64/share/man:/usr/local/man:/usr/share/man:/usr/man:/share/man'
    'MINGW_CHOST' => 'x86_64-w64-mingw32'
    'MINGW_PACKAGE_PREFIX' => 'mingw-w64-x86_64'
    'MINGW_PREFIX' => '/mingw64'
    'MSYSTEM' => 'MINGW64'
    'MSYSTEM_CARCH' => 'x86_64'
    'MSYSTEM_CHOST' => 'x86_64-w64-mingw32'
    'MSYSTEM_PREFIX' => '/mingw64'
    'NUMBER_OF_PROCESSORS' => '12'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OneDriveCommercial' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OnlineServices' => 'Online Services'
    'ORIGINAL_PATH' => 'C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files (x86)\\HP\\HP OCR\\DB_Lib;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\GitHub CLI;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand'
    'ORIGINAL_TEMP' => '/tmp'
    'ORIGINAL_TMP' => '/tmp'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'PATH' => 'C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files (x86)\\HP\\HP OCR\\DB_Lib;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\GitHub CLI;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl;C:\\ProgramData\\ComposerSetup\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW'
    'PKG_CONFIG_PATH' => '/mingw64/lib/pkgconfig:/mingw64/share/pkgconfig'
    'PKG_CONFIG_SYSTEM_INCLUDE_PATH' => '/mingw64/include'
    'PKG_CONFIG_SYSTEM_LIBRARY_PATH' => '/mingw64/lib'
    'platformcode' => 'AN'
    'PLINK_PROTOCOL' => 'ssh'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 186 Stepping 3, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => 'ba03'
    'ProgramData' => 'C:\\ProgramData'
    'PROGRAMFILES' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PS1' => '\\[]633;A\\]\\[\\033]0;$TITLEPREFIX:$PWD\\007\\]\\n\\[\\033[32m\\]\\u@\\h \\[\\033[35m\\]$MSYSTEM \\[\\033[33m\\]\\w\\[\\033[36m\\]`__git_ps1`\\[\\033[0m\\]\\n$ \\[]633;B\\]'
    'PSModulePath' => 'C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe'
    'SHLVL' => '1'
    'SSH_ASKPASS' => '/mingw64/bin/git-askpass.exe'
    'SYSTEMDRIVE' => 'C:'
    'SYSTEMROOT' => 'C:\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.103.1'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMPDIR' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'EGOV-L-046'
    'USERDOMAIN_ROAMINGPROFILE' => 'EGOV-L-046'
    'USERNAME' => 'User'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Users\\User\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-34508b9eb2-sock'
    'WINDIR' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    '_' => '/usr/bin/winpty'
    '__COMPAT_LAYER' => 'DetectorsAppHealth'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => **********.2796
    'REQUEST_TIME' => **********
    'argv' => [
        0 => 'yii'
        1 => 'permission-cache/check'
        2 => '1'
    ]
    'argc' => 3
]
2025-08-21 16:34:21 [-][-][-][error][yii\console\UnknownCommandException] yii\base\InvalidRouteException: Unable to resolve the request: permission-cache/ in C:\Web\Reclassering\vendor\yiisoft\yii2\base\Controller.php:155
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(555): yii\console\Controller->runAction()
#2 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#3 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#4 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(387): yii\console\Application->handleRequest()
#5 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#6 {main}

Next yii\console\UnknownCommandException: Unknown command "permission-cache/". in C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php:183
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(387): yii\console\Application->handleRequest()
#2 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#3 {main}
2025-08-21 16:34:20 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ACLOCAL_PATH' => '/mingw64/share/aclocal:/usr/share/aclocal'
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'ChocolateyInstall' => 'C:\\ProgramData\\chocolatey'
    'ChocolateyLastPathUpdate' => '133976692032696195'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_32272_LHDCWCCBZXNHJFHD'
    'COLORTERM' => 'truecolor'
    'COMMONPROGRAMFILES' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'EGOV-L-046'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CONFIG_SITE' => '/etc/config.site'
    'DISPLAY' => 'needs-to-be-defined'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_6764_1262719628' => '1'
    'EFC_6764_1592913036' => '1'
    'EFC_6764_2283032206' => '1'
    'EFC_6764_2775293581' => '1'
    'EFC_6764_3789132940' => '1'
    'EXEPATH' => 'C:\\Program Files\\Git\\bin'
    'FPS_BROWSER_APP_PROFILE_STRING' => 'Internet Explorer'
    'FPS_BROWSER_USER_PROFILE_STRING' => 'Default'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'HOME' => 'C:\\Users\\<USER>\\Users\\User'
    'HOSTNAME' => 'eGov-L-046'
    'IGCCSVC_DB' => 'AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg=='
    'INFOPATH' => '/mingw64/local/info:/mingw64/share/info:/usr/local/info:/usr/share/info:/usr/info:/share/info'
    'LANG' => 'en_US.UTF-8'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\EGOV-L-046'
    'MANPATH' => '/mingw64/local/man:/mingw64/share/man:/usr/local/man:/usr/share/man:/usr/man:/share/man'
    'MINGW_CHOST' => 'x86_64-w64-mingw32'
    'MINGW_PACKAGE_PREFIX' => 'mingw-w64-x86_64'
    'MINGW_PREFIX' => '/mingw64'
    'MSYSTEM' => 'MINGW64'
    'MSYSTEM_CARCH' => 'x86_64'
    'MSYSTEM_CHOST' => 'x86_64-w64-mingw32'
    'MSYSTEM_PREFIX' => '/mingw64'
    'NUMBER_OF_PROCESSORS' => '12'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OneDriveCommercial' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OnlineServices' => 'Online Services'
    'ORIGINAL_PATH' => 'C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files (x86)\\HP\\HP OCR\\DB_Lib;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\GitHub CLI;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand'
    'ORIGINAL_TEMP' => '/tmp'
    'ORIGINAL_TMP' => '/tmp'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'PATH' => 'C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files (x86)\\HP\\HP OCR\\DB_Lib;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\GitHub CLI;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl;C:\\ProgramData\\ComposerSetup\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW'
    'PKG_CONFIG_PATH' => '/mingw64/lib/pkgconfig:/mingw64/share/pkgconfig'
    'PKG_CONFIG_SYSTEM_INCLUDE_PATH' => '/mingw64/include'
    'PKG_CONFIG_SYSTEM_LIBRARY_PATH' => '/mingw64/lib'
    'platformcode' => 'AN'
    'PLINK_PROTOCOL' => 'ssh'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 186 Stepping 3, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => 'ba03'
    'ProgramData' => 'C:\\ProgramData'
    'PROGRAMFILES' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PS1' => '\\[]633;A\\]\\[\\033]0;$TITLEPREFIX:$PWD\\007\\]\\n\\[\\033[32m\\]\\u@\\h \\[\\033[35m\\]$MSYSTEM \\[\\033[33m\\]\\w\\[\\033[36m\\]`__git_ps1`\\[\\033[0m\\]\\n$ \\[]633;B\\]'
    'PSModulePath' => 'C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe'
    'SHLVL' => '1'
    'SSH_ASKPASS' => '/mingw64/bin/git-askpass.exe'
    'SYSTEMDRIVE' => 'C:'
    'SYSTEMROOT' => 'C:\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.103.1'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMPDIR' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'EGOV-L-046'
    'USERDOMAIN_ROAMINGPROFILE' => 'EGOV-L-046'
    'USERNAME' => 'User'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Users\\User\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-34508b9eb2-sock'
    'WINDIR' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    '_' => '/usr/bin/winpty'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1755790460.9856
    'REQUEST_TIME' => 1755790460
    'argv' => [
        0 => 'yii'
        1 => 'permission-cache/'
        2 => 'show'
        3 => '7'
    ]
    'argc' => 4
]
2025-10-01 13:59:28 [-][-][-][error][yii\console\UnknownCommandException] yii\base\InvalidRouteException: Unable to resolve the request "shell". in C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php:564
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#2 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(387): yii\console\Application->handleRequest()
#3 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#4 {main}

Next yii\console\UnknownCommandException: Unknown command "shell". in C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php:183
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(387): yii\console\Application->handleRequest()
#2 C:\Web\Reclassering\yii(23): yii\base\Application->run()
#3 {main}
2025-10-01 13:59:27 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ACLOCAL_PATH' => '/mingw64/share/aclocal:/usr/share/aclocal'
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'ChocolateyInstall' => 'C:\\ProgramData\\chocolatey'
    'ChocolateyLastPathUpdate' => '133976692032696195'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_24404_HMWUSHOANFXBWPTT'
    'COLORTERM' => 'truecolor'
    'COMMONPROGRAMFILES' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'EGOV-L-046'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CONFIG_SITE' => '/etc/config.site'
    'DISPLAY' => 'needs-to-be-defined'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_4752_1262719628' => '1'
    'EFC_4752_1592913036' => '1'
    'EFC_4752_2283032206' => '1'
    'EFC_4752_2775293581' => '1'
    'EFC_4752_3789132940' => '1'
    'EXEPATH' => 'C:\\Program Files\\Git\\bin'
    'FPS_BROWSER_APP_PROFILE_STRING' => 'Internet Explorer'
    'FPS_BROWSER_USER_PROFILE_STRING' => 'Default'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'GIT_PAGER' => 'cat'
    'HOME' => 'C:\\Users\\<USER>\\Users\\User'
    'HOSTNAME' => 'eGov-L-046'
    'IGCCSVC_DB' => 'AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg=='
    'INFOPATH' => '/mingw64/local/info:/mingw64/share/info:/usr/local/info:/usr/share/info:/usr/info:/share/info'
    'LANG' => 'en_US.UTF-8'
    'LESS' => '-FX'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\EGOV-L-046'
    'MANPATH' => '/mingw64/local/man:/mingw64/share/man:/usr/local/man:/usr/share/man:/usr/man:/share/man'
    'MINGW_CHOST' => 'x86_64-w64-mingw32'
    'MINGW_PACKAGE_PREFIX' => 'mingw-w64-x86_64'
    'MINGW_PREFIX' => '/mingw64'
    'MSYSTEM' => 'MINGW64'
    'MSYSTEM_CARCH' => 'x86_64'
    'MSYSTEM_CHOST' => 'x86_64-w64-mingw32'
    'MSYSTEM_PREFIX' => '/mingw64'
    'NUMBER_OF_PROCESSORS' => '12'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OneDriveCommercial' => 'C:\\Users\\<USER>\\OneDrive - GOV Suriname'
    'OnlineServices' => 'Online Services'
    'ORIGINAL_PATH' => 'C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files (x86)\\HP\\HP OCR\\DB_Lib;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\GitHub CLI;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand'
    'ORIGINAL_TEMP' => '/tmp'
    'ORIGINAL_TMP' => '/tmp'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'PAGER' => 'cat'
    'PATH' => 'C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PHP\\v8.3;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;C:\\Program Files (x86)\\HP\\HP OCR\\DB_Lib;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\GitHub CLI;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Program Files\\HP\\Common\\HPDestPlgIn;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl;C:\\ProgramData\\ComposerSetup\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW'
    'PKG_CONFIG_PATH' => '/mingw64/lib/pkgconfig:/mingw64/share/pkgconfig'
    'PKG_CONFIG_SYSTEM_INCLUDE_PATH' => '/mingw64/include'
    'PKG_CONFIG_SYSTEM_LIBRARY_PATH' => '/mingw64/lib'
    'platformcode' => 'AN'
    'PLINK_PROTOCOL' => 'ssh'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 186 Stepping 3, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => 'ba03'
    'ProgramData' => 'C:\\ProgramData'
    'PROGRAMFILES' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PS1' => '\\[]633;A\\]\\[\\033]0;$TITLEPREFIX:$PWD\\007\\]\\n\\[\\033[32m\\]\\u@\\h \\[\\033[35m\\]$MSYSTEM \\[\\033[33m\\]\\w\\[\\033[36m\\]`__git_ps1`\\[\\033[0m\\]\\n$ \\[]633;B\\]'
    'PSModulePath' => 'C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe'
    'SHLVL' => '1'
    'SSH_ASKPASS' => '/mingw64/bin/git-askpass.exe'
    'SYSTEMDRIVE' => 'C:'
    'SYSTEMROOT' => 'C:\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.104.2'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMPDIR' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'EGOV-L-046'
    'USERDOMAIN_ROAMINGPROFILE' => 'EGOV-L-046'
    'USERNAME' => 'User'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Users\\User\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-34508b9eb2-sock'
    'VSCODE_PYTHON_AUTOACTIVATE_GUARD' => '1'
    'WINDIR' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    '_' => '/usr/bin/winpty'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1759323567.7666
    'REQUEST_TIME' => 1759323567
    'argv' => [
        0 => 'yii'
        1 => 'shell'
        2 => '--interactive=0'
        3 => '--command=echo \'Checking notification_user table structure and sample data:\';  = \\common\\models\\NotificationUser::find()->limit(3)->all(); foreach( as ) { echo \'ID: \' . ->id . \', Link: \' . ->link . PHP_EOL; }'
    ]
    'argc' => 4
]
