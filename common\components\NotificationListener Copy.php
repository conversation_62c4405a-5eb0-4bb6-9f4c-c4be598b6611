<?php

namespace common\components;

use Yii;
use yii\base\Behavior;
use yii\web\Application;
use common\components\NotificationManager;
use common\models\NotificationTrigger;

class NotificationListenerCopy extends Behavior
{
    public function events()
    {
        return [
            Application::EVENT_AFTER_ACTION => 'checkAndTrigger',
        ];
    }

    public function checkAndTrigger($event)
    {
        $route = Yii::$app->controller->route;
        $trigger = NotificationTrigger::find()->where(['route' => $route])->one();
        if (!$trigger) return;

        $request = Yii::$app->request;
        $modelClass = $trigger->model_class;

        if (!class_exists($modelClass)) {
            Yii::error("Model class {$modelClass} not found for route {$route}", __METHOD__);
            return;
        }

        $model = null;

        // Handle different trigger types
        switch ($trigger->trigger_type) {
            case 'create':
                if (!$request->isPost) return;
                $model = $this->findModelForCreate($event, $trigger, $modelClass);
                break;
            case 'update':
                if (!($request->isPost || $request->isPut || $request->isPatch)) return;
                $model = $this->findModelForUpdate($trigger, $modelClass);
                break;
            case 'view':
                if (!$request->isGet) return;
                $model = $this->findModelForView($trigger, $modelClass);
                break;
            default:
                Yii::warning("Unknown trigger type: {$trigger->trigger_type}", __METHOD__);
                return;
        }

        if (!$model) {
            Yii::warning("No model found for notification trigger on route: {$route}", __METHOD__);
            return;
        }

        NotificationManager::trigger(
            $trigger->notification_key,
            NotificationManager::buildMessageData($model, $trigger)
        );
    }

    private function findModelForCreate($event, $trigger, $modelClass)
    {
        $request = Yii::$app->request;
        $result = $event->result;

        // For AJAX responses, check if the response indicates success
        if (is_array($result)) {
            // Check for success indicators in AJAX response
            if (isset($result['forceReload']) || isset($result['success'])) {
                // Try to find the model from the response or from the last created record
                if (isset($result['model']) && $result['model'] instanceof $modelClass) {
                    return $result['model'];
                }

                // If no model in response, try to find the most recently created record
                // This is a fallback for cases where the model isn't passed in the response
                $model = $modelClass::find()->orderBy(['id' => SORT_DESC])->one();
                if ($model && $this->isRecentlyCreated($model)) {
                    return $model;
                }
            }
        }

        // For non-AJAX responses, try to get model ID from request or response
        $idParam = $trigger->model_id_param;
        $id = $request->getBodyParam($idParam) ?? $request->getQueryParam($idParam);

        if ($id) {
            return $modelClass::findOne($id);
        }

        return null;
    }

    private function findModelForUpdate($trigger, $modelClass)
    {
        $request = Yii::$app->request;
        $idParam = $trigger->model_id_param;
        $id = $request->getBodyParam($idParam) ?? $request->getQueryParam($idParam);

        if ($id) {
            return $modelClass::findOne($id);
        }

        return null;
    }

    private function findModelForView($trigger, $modelClass)
    {
        $request = Yii::$app->request;
        $idParam = $trigger->model_id_param;
        $id = $request->getQueryParam($idParam);

        if ($id) {
            return $modelClass::findOne($id);
        }

        return null;
    }

    private function isRecentlyCreated($model)
    {
        // Check if the model was created within the last 5 seconds
        if (isset($model->created_at)) {
            $createdTime = strtotime($model->created_at);
            return (time() - $createdTime) <= 5;
        }

        return true; // If no created_at field, assume it's recent
    }
}
