<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "document_upload_permission".
 *
 * @property int $id
 * @property int $document_id
 * @property int $user_id
 * @property int|null $can_view
 * @property int $created_at
 * @property int $updated_at
 *
 * @property DocumentUpload $document
 * @property User $user
 */
class DocumentPermission extends \yii\db\ActiveRecord
{


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'document_permission';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['can_view'], 'default', 'value' => 1],
            [['document_id', 'user_id', 'created_at', 'updated_at'], 'required'],
            [['document_id', 'user_id', 'can_view', 'created_at', 'updated_at'], 'integer'],
            [['document_id'], 'exist', 'skipOnError' => true, 'targetClass' => DocumentUpload::class, 'targetAttribute' => ['document_id' => 'id']],
            [['user_id'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['user_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'document_id' => 'Document ID',
            'user_id' => 'User ID',
            'can_view' => 'Can View',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[Document]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getDocument()
    {
        return $this->hasOne(DocumentUpload::class, ['id' => 'document_id']);
    }

    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(User::class, ['id' => 'user_id']);
    }
}
