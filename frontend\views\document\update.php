<?php

use yii\helpers\Html;
use kartik\form\ActiveForm;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $model common\models\Rapport */

$this->title = 'Update Document';

// Get rapport data using the helper function
$documentData = getDocument($model->id);
?>
<div class="rapport-update row">
    <!-- Left side - Questions and Answers -->
    <div class="col-md-6">
        <!-- Status -->
        <h5>
            Document status:
            <span class="badge bg-danger rounded-2 ms-1">
                <?= $model->workflowStatus->label ?>
            </span>
        </h5>

        <div class="w-100 bg-secondary mb-4" style="height: 1px;"></div>

        <div class="card rounded-3">
            <div class="card-body">
                <h3 class="mb-3"><?= $documentData['data']['document']['type'] ?></h3>
                <?php $form = ActiveForm::begin([
                    'id' => 'rapport-form',
                    'enableAjaxValidation' => false,
                    'options' => [
                        'data-redirect-url' => Url::to(['view', 'id' => $model->id])
                    ]
                ]); ?>

                <?php if (!empty($documentData['data']['questions_and_answers'])): ?>
                    <div id="questions-container">
                        <?php foreach ($documentData['data']['questions_and_answers'] as $qa): ?>
                            <div class="form-group mb-3">
                                <label class="control-label">
                                    <?= $qa['question']['number'] ?>. <?= Html::encode($qa['question']['text']) ?>
                                </label>
                                <?= Html::textInput(
                                    "Answers[{$qa['question']['id']}]",
                                    $qa['answer']['text'] ?? '',
                                    [
                                        'class' => 'form-control',
                                        'required' => true,
                                        'data-question-id' => $qa['question']['id']
                                    ]
                                ) ?>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <div class="form-group">
                        <?= Html::submitButton('Save', ['class' => 'btn btn-success rounded-2']) ?>
                    </div>
                <?php endif; ?>

                <?php ActiveForm::end(); ?>
            </div>
        </div>
    </div>

    <!-- Right side - Comments -->
    <div class="col-md-6">
        <div class="card rounded-3">
            <div class="card-body">
                <h4>Comments</h4>
                <?php if (!empty($model->documentFeedbacks)): ?>
                    <?php
                    $sortedFeedbacks = $model->documentFeedbacks;
                    usort($sortedFeedbacks, function ($a, $b) {
                        return strtotime($b->created_at) - strtotime($a->created_at); // DESC order
                    });
                    ?>

                    <?php foreach ($sortedFeedbacks as $feedback): ?>
                        <div class="card mb-3">
                            <div class="card-body">
                                <h6 class="card-subtitle mb-2 text-muted">
                                    By <?= Html::encode($feedback->createdBy->username) ?>
                                    on <?= Yii::$app->formatter->asDatetime($feedback->created_at) ?>
                                </h6>
                                <p class="card-text"><?= Html::encode($feedback->comment) ?></p>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p class="text-muted">No comments yet.</p>
                <?php endif; ?>

            </div>
        </div>
    </div>
</div>

<?php
$script = <<<JS
// Initialize Toast configuration
const Toast = Swal.mixin({
    toast: true,
    position: 'top-end',
    showConfirmButton: false,
    timer: 3000,
    timerProgressBar: true,
    didOpen: (toast) => {
        toast.addEventListener('mouseenter', Swal.stopTimer)
        toast.addEventListener('mouseleave', Swal.resumeTimer)
    }
});

$('#rapport-form').on('beforeSubmit', function(e) {
    e.preventDefault(); // Prevent the default form submission
    
    let form = $(this);
    $.ajax({
        url: form.attr('action'),
        type: 'POST',
        data: form.serialize(),
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                Toast.fire({
                    icon: 'success',
                    title: response.message
                });
                
                // Optional: Redirect after delay
                setTimeout(function() {
                    window.location.href = form.data('redirect-url') || window.location.href;
                }, 1500);
            } else {
                Toast.fire({
                    icon: 'error',
                    title: response.message || 'An error occurred'
                });
            }
        },
        error: function(xhr, status, error) {
            Toast.fire({
                icon: 'error',
                title: 'Error submitting form: ' + error
            });
        }
    });
    return false;
});
JS;
$this->registerJs($script);
?>