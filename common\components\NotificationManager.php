<?php

namespace common\components;

use Yii;
use yii\base\Component;
use yii\web\Request;
use common\models\Notification;
use common\models\NotificationTrigger;
use common\models\NotificationUser;

class NotificationManager extends Component
{
    /**
     * Main entry point for route-based triggers.
     */
    public function checkAndTrigger(string $route, Request $request, string $phase = 'before', array $extraParams = []): void
    {
        $triggers = NotificationTrigger::find()->where(['route' => $route])->all();
        if (empty($triggers)) {
            return;
        }

        foreach ($triggers as $trigger) {
            // Match request type
            if ($trigger->request_type === 'AJAX' && !$request->isAjax) {
                continue;
            }
            if ($trigger->request_type === 'NON_AJAX' && $request->isAjax) {
                continue;
            }

            // Handle create routes: skip BEFORE, only handle AFTER
            if ($phase === 'before' && str_contains($route, '/create')) {
                continue;
            }

            $notification = Notification::findOne(['key' => $trigger->notification_key, 'enabled' => 1]);
            if (!$notification) {
                continue;
            }

            // Merge GET, POST, and any extra parameters passed in (like model ID after save)
            $replacements = array_merge($request->get(), $request->post(), $extraParams);

            // Auto-detect ID if not already provided
            $replacements = $this->autoDetectId($replacements, $route, $request);

            // Apply templates
            $message = $this->applyTemplate($notification->message_template, $replacements);
            $link = $this->applyTemplate($trigger->link_template, $replacements);

            // Find users via roles
            $users = (new \yii\db\Query())
                ->select('u.id')
                ->from('user u')
                ->innerJoin('notification_role nr', 'nr.role_id = u.role_id')
                ->where(['nr.notification_id' => $notification->id])
                ->all();

            foreach ($users as $user) {
                $nu = new NotificationUser([
                    'notification_id' => $notification->id,
                    'user_id'         => $user['id'],
                    'message'         => $message,
                    'link'            => $link,
                ]);
                $nu->save(false);
            }

            if ($notification->send_email) {
                foreach ($users as $user) {
                    $this->sendEmail($user['id'], $notification, $message, $link);
                }
            }
        }
    }

    /**
     * Auto-detect ID from various sources if not already provided.
     * This makes the system work automatically without manual code changes.
     */
    protected function autoDetectId(array $replacements, string $route, Request $request): array
    {
        // If ID is already provided, return as-is
        if (isset($replacements['id']) && !empty($replacements['id'])) {
            return $replacements;
        }

        // Try to get ID from Yii::$app->params['lastCreatedId'] (if set by controller)
        if (isset(Yii::$app->params['lastCreatedId'])) {
            $replacements['id'] = Yii::$app->params['lastCreatedId'];
            return $replacements;
        }

        // For create routes, try to find the most recently created record
        if (str_contains($route, '/create') && $request->isPost) {
            // Extract controller name from route (e.g., '/document/create' -> 'Document')
            $parts = explode('/', trim($route, '/'));
            if (count($parts) >= 1) {
                $controllerName = $parts[0];
                $modelClass = $this->guessModelClass($controllerName);

                if ($modelClass && class_exists($modelClass)) {
                    try {
                        // Get the most recently created record by this user
                        $model = $modelClass::find()
                            ->where(['created_by' => Yii::$app->user->id])
                            ->orderBy(['id' => SORT_DESC])
                            ->limit(1)
                            ->one();

                        if ($model && isset($model->id)) {
                            $replacements['id'] = $model->id;
                        }
                    } catch (\Exception $e) {
                        Yii::warning("Failed to auto-detect ID for route {$route}: " . $e->getMessage(), __METHOD__);
                    }
                }
            }
        }

        // For update/view routes, try to get ID from GET params
        if ((str_contains($route, '/update') || str_contains($route, '/view')) && isset($request->get()['id'])) {
            $replacements['id'] = $request->get()['id'];
        }

        return $replacements;
    }

    /**
     * Guess the model class name from controller name.
     */
    protected function guessModelClass(string $controllerName): ?string
    {
        // Convert controller name to model class name
        // e.g., 'document' -> 'common\models\Document'
        $modelName = str_replace(' ', '', ucwords(str_replace('-', ' ', $controllerName)));

        // Try common namespace first
        $commonClass = "common\\models\\{$modelName}";
        if (class_exists($commonClass)) {
            return $commonClass;
        }

        // Try frontend namespace
        $frontendClass = "frontend\\models\\{$modelName}";
        if (class_exists($frontendClass)) {
            return $frontendClass;
        }

        // Try backend namespace
        $backendClass = "backend\\models\\{$modelName}";
        if (class_exists($backendClass)) {
            return $backendClass;
        }

        return null;
    }

    /**
     * Replace {{placeholders}} with request or extra params.
     */
    protected function applyTemplate(?string $template, array $params): ?string
    {
        if (!$template) {
            return null;
        }

        return preg_replace_callback('/{{(.*?)}}/', function ($matches) use ($params) {
            $key = trim($matches[1]);
            return $params[$key] ?? '';
        }, $template);
    }

    protected function sendEmail($userId, Notification $notification, string $message, ?string $link = null): void
    {
        $user = \common\models\User::findOne($userId);
        if (!$user || !$user->email) {
            return;
        }

        Yii::$app->mailer->compose()
            ->setTo($user->email)
            ->setSubject($notification->title)
            ->setTextBody($message . ($link ? "\n\n" . Yii::$app->urlManager->createAbsoluteUrl($link) : ''))
            ->send();
    }
}
