<?php

namespace common\components;

use common\components\SignatureHelper;
use Yii;
use yii\bootstrap5\Html;

class DocumentButtonVisibilityHelper
{
    private static array $cache = [];

    private static function getData($key)
    {
        if (!isset(self::$cache[$key])) {
            self::$cache[$key] = getDocument($key)['data'];
        }
        return self::$cache[$key];
    }

    public static function showDocumentBtn($key): bool
    {
        $data = self::getData($key);
        return $data['status'] === 'final';
    }

    public static function showSignatureBtn($key): bool
    {
        $data = self::getData($key);
        return $data['status'] !== 'final'
            && SignatureHelper::hasSignatureFromRoles($data['signatures'], [1, 3]);
    }

    public static function showViewBtn($key): bool
    {
        $data = self::getData($key);
        return $data['status'] !== 'final';
    }

    // Only show update btn if status is medewerker or if user is superUser, hoofd or onderhoofd and status is not final.
    public static function showUpdateBtn($key): bool
    {
        $data = self::getData($key);
        return  $data['status'] === "hoofd" || (isHoofdOrOnderhoofd()) &&  Yii::$app->id === 'app-backend';
    }

    public static function showDeleteBtn($key): bool
    {
        $data = self::getData($key);
        return $data['status'] !== 'final' && isAdminOrSuperAdmin() && Yii::$app->id === 'app-backend';
    }

    public static function BtnCreator(array $params)
    {
        $icon = $params['icon'] ?? 'circle';
        $url = $params['url'] ?? '#';
        $title = $params['title'] ?? Yii::t('app', 'Button');
        $class = $params['class'] ?? 'btn btn-sm btn-outline-secondary';
        $target = $params['target'] ?? null;
        $role = $params['role'] ?? null;
        $tooltip = $params['tooltip'] ?? true;

        $options = [
            'title' => $title,
            'class' => $class,
        ];

        if ($tooltip) {
            $options['data-toggle'] = 'tooltip';
        }

        if ($target) {
            $options['target'] = $target;
        }

        if ($role) {
            $options['role'] = $role;
        }

        return Html::a(
            "<i data-lucide=\"{$icon}\" style=\"width:18px; height:18px\"></i>",
            $url,
            $options
        );
    }
}
