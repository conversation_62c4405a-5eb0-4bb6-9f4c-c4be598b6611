<?php

use yii\helpers\Html;
use yii\helpers\Url;

/** @var yii\data\ActiveDataProvider $dataProvider */

$this->title = 'Uploaded Documents';

$this->registerJs(<<<JS
$('.document-card').on('click', function() {
    var url = $(this).data('url');
    window.open(url, '_blank');
});

// Delete document with confirmation dialog
$('.delete-doc').on('click', function(e) {
    e.stopPropagation();
    const docId = $(this).data('id');
    const card = $(this).closest('.document-card');
    const url = $(this).data('url');

    Swal.fire({
        title: 'Are you sure?',
        text: 'This document will be permanently deleted.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        // cancelButtonColor: '#3085d6',
        confirmButtonText: 'Yes, delete it!',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            $.post({
                url: url,
                data: {id: docId},
                success: function() {
                    card.fadeOut(300, function() {
                        $(this).remove();
                        Toast.fire({
                            icon: 'success',
                            title: 'Document deleted successfully'
                        });
                    });
                },
                error: function() {
                    Toast.fire({
                        icon: 'error',
                        title: 'Error deleting document'
                    });
                }
            });
        }
    });
});
JS)
?>

<style>
    .document-card {
        transition: box-shadow 0.2s;
        position: relative;
    }

    .delete-doc {
        position: absolute;
        top: 8px;
        right: 12px;
        color: #dc3545;
        background: rgba(255, 255, 255, 0.85);
        border: none;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        opacity: 0;
        pointer-events: none;
        transition: background 0.2s, opacity 0.2s;
        cursor: pointer;
    }

    .document-card:hover {
        box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
        z-index: 2;
    }

    .document-card:hover .delete-doc {
        opacity: 0.85;
        pointer-events: auto;
    }

    .delete-doc:hover {
        background: #fff;
        opacity: 1;
    }
</style>

<div class="d-flex justify-content-between align-items-center mb-3">
    <h2><?= Html::encode($this->title) ?></h2>
    <div class="d-flex">
        <a href="<?= Url::to(['upload']) ?>" class="btn btn-outline-primary rounded-2 ms-3">
            <i data-lucide="cloud-upload" style="width: 18px; height: 18px;"></i>
            Upload Document</a>
        <?php if (canManageUploadedDocuments()): ?>
            <a href="<?= Url::to(['manage-uploaded-documents']) ?>" class="btn btn-outline-dark rounded-2 ms-2">
                <i data-lucide="user-round-cog" style="width: 18px; height: 18px;"></i>
                Beheer documenten</a>
        <?php endif; ?>
    </div>
</div>

<div class="row">
    <?php foreach ($dataProvider->getModels() as $model): ?>
        <div class="col-md-3">
            <div class="card mb-4 rounded-2 document-card" style="cursor:pointer; height: 100%"
                data-id="<?= $model->encryptedId ?>"
                data-url="<?= $model->serveUrl ?>">
                <?php if (canDeleteUploadedDocuments()): ?>
                    <button class="delete-doc" data-id="<?= $model->encryptedId ?>" data-url="<?= Url::to(['delete', 'id' => $model->encryptedId]) ?>" title="Delete">
                        <i data-lucide="trash-2"></i>
                    </button>
                <?php endif; ?>
                <div class="card-body text-center">
                    <p>
                        <i class="fas fa-file-pdf text-danger fa-3x mb-2"></i>
                    </p>
                    <p class="fs-6" style="margin-bottom: 0;">
                        <?php
                        // Remove 'uploaded_' prefix and underscores from filename (without extension)
                        $displayName = preg_replace('/^uploaded_/', '', pathinfo($model->filename, PATHINFO_FILENAME));
                        $displayName = str_replace('_', ' ', $displayName);
                        echo Html::encode($displayName . '.' . pathinfo($model->filename, PATHINFO_EXTENSION));
                        ?>
                    </p>
                    <p class="text-muted" style="font-size:12px;margin-bottom: 0;">
                        <?= Yii::$app->formatter->asShortSize($model->file_size) ?>
                    </p>
                    <p class="text-muted" style="font-size:12px;margin-bottom: 0;">
                        Geplaatst door: <?= $model->user ? $model->user->username : 'Unknown' ?>
                    </p>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
</div>