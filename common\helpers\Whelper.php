<?php

use yii\helpers\Html;
use yii\web\Response;

if (!function_exists('getStatusTransitions')) {
    function getStatusTransitions($model)
    {
        $result = [];
        if ($model->hasWorkflowStatus()) {
            // Current status
            $currentStatus = $model->getWorkflowStatus()->getId();

            // Get possible transitions for the WorkflowSource
            $transitions = $model
                ->getWorkflowSource()
                ->getTransitions($model->getWorkflowStatus()->getId());

            foreach ($transitions as $transition) {
                $result[] = $transition->getEndStatus()->getId();
            }
        }
        return $result;
    }
}

if (!function_exists('goToNextStatus')) {
    function goToNextStatus($model)
    {
        try {
            if (!$model->hasWorkflowStatus()) {
                throw new \Exception('Model does not have workflow status');
            }

            $transitions = $model->getWorkflowSource()->getTransitions($model->getWorkflowStatus()->getId());
            if (empty($transitions)) {
                throw new \Exception('No forward transitions available from current status');
            }

            // Get the first available forward transition
            $nextTransition = end($transitions);
            $nextStatus = $nextTransition->getEndStatus()->getId();

            $model->sendToStatus($nextStatus);
            $model->save();
        } catch (\Exception $e) {
            throw new \Exception('Error during status transition: ' . $e->getMessage());
        }
    }
}

if (!function_exists('goToPreviousStatus')) {
    function goToPreviousStatus($model)
    {
        try {
            if (!$model->hasWorkflowStatus()) {
                throw new \Exception('Model does not have workflow status');
            }

            $currentStatus = $model->getWorkflowStatus()->getId();
            $transitions = $model->getWorkflowSource()->getTransitions($currentStatus);

            // dd($transitions); // Debug the transitions array

            if (empty($transitions)) {
                throw new \Exception('No backward transitions available from current status');
            }

            // Handle transitions based on current status
            switch ($currentStatus) {
                case 'onderhoofd':
                    // For onderhoofd, only allow transition back to medewerker
                    foreach ($transitions as $transition) {
                        if ($transition->getEndStatus()->getId() === 'medewerker') {
                            $model->sendToStatus('medewerker');
                            $model->save();
                            return;
                        }
                    }
                    break;
                case 'hoofd':
                    foreach ($transitions as $transition) {
                        if ($transition->getEndStatus()->getId() === 'medewerker') {
                            $model->sendToStatus('medewerker');
                            $model->save();
                            return;
                        }
                    }
                    break;
            }

            throw new \Exception('No valid backward transition found for status: ' . $currentStatus);
        } catch (\Exception $e) {
            throw new \Exception('Error during status transition: ' . $e->getMessage());
        }
    }
}

if (!function_exists('goToMedewerkerStatus')) {
    function goToMedewerkerStatus($model)
    {
        try {
            if (!$model->hasWorkflowStatus()) {
                throw new \Exception('Model does not have workflow status');
            }

            $model->sendToStatus('medewerker');
            $model->save();
        } catch (\Exception $e) {
            throw new \Exception('Error during status transition: ' . $e->getMessage());
        }
    }
}
