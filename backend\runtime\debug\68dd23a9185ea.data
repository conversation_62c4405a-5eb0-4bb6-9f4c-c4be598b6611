a:14:{s:6:"config";s:15911:"a:5:{s:10:"phpVersion";s:5:"8.3.3";s:10:"yiiVersion";s:6:"2.0.53";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.53";s:4:"name";s:3:"FMZ";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.3.3";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:71:{s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:47:"C:\Web\Reclassering\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:57:"C:\Web\Reclassering\vendor/yiisoft/yii2-symfonymailer/src";}}s:29:"hail812/yii2-adminlte-widgets";a:3:{s:4:"name";s:29:"hail812/yii2-adminlte-widgets";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:25:"@hail812/adminlte/widgets";s:60:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte-widgets/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.12.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap4/src";}}s:22:"hail812/yii2-adminlte3";a:3:{s:4:"name";s:22:"hail812/yii2-adminlte3";s:7:"version";s:7:"1.1.9.0";s:5:"alias";a:1:{s:18:"@hail812/adminlte3";s:53:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte3/src";}}s:18:"kartik-v/yii2-mpdf";a:3:{s:4:"name";s:18:"kartik-v/yii2-mpdf";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:12:"@kartik/mpdf";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-mpdf/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-dialog/src";}}s:23:"kartik-v/yii2-popover-x";a:3:{s:4:"name";s:23:"kartik-v/yii2-popover-x";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:15:"@kartik/popover";s:54:"C:\Web\Reclassering\vendor/kartik-v/yii2-popover-x/src";}}s:21:"kartik-v/yii2-builder";a:3:{s:4:"name";s:21:"kartik-v/yii2-builder";s:7:"version";s:7:"1.6.9.0";s:5:"alias";a:1:{s:15:"@kartik/builder";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-builder/src";}}s:22:"kartik-v/yii2-editable";a:3:{s:4:"name";s:22:"kartik-v/yii2-editable";s:7:"version";s:7:"1.8.0.0";s:5:"alias";a:1:{s:16:"@kartik/editable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-editable/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:7:"3.5.3.0";s:5:"alias";a:1:{s:12:"@kartik/grid";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-grid/src";}}s:21:"kartik-v/yii2-helpers";a:3:{s:4:"name";s:21:"kartik-v/yii2-helpers";s:7:"version";s:7:"1.3.9.0";s:5:"alias";a:1:{s:15:"@kartik/helpers";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-helpers/src";}}s:24:"kartik-v/yii2-checkbox-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-checkbox-x";s:7:"version";s:7:"1.0.7.0";s:5:"alias";a:1:{s:16:"@kartik/checkbox";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-checkbox-x/src";}}s:26:"kartik-v/yii2-context-menu";a:3:{s:4:"name";s:26:"kartik-v/yii2-context-menu";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:13:"@kartik/cmenu";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-context-menu/src";}}s:25:"kartik-v/yii2-datecontrol";a:3:{s:4:"name";s:25:"kartik-v/yii2-datecontrol";s:7:"version";s:7:"1.9.9.0";s:5:"alias";a:1:{s:19:"@kartik/datecontrol";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-datecontrol/src";}}s:22:"kartik-v/yii2-sortable";a:3:{s:4:"name";s:22:"kartik-v/yii2-sortable";s:7:"version";s:7:"1.2.2.0";s:5:"alias";a:1:{s:16:"@kartik/sortable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable/src";}}s:25:"kartik-v/yii2-field-range";a:3:{s:4:"name";s:25:"kartik-v/yii2-field-range";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:13:"@kartik/field";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-field-range/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-httpclient/src";}}s:25:"kartik-v/yii2-detail-view";a:3:{s:4:"name";s:25:"kartik-v/yii2-detail-view";s:7:"version";s:7:"1.8.7.0";s:5:"alias";a:1:{s:14:"@kartik/detail";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-detail-view/src";}}s:24:"kartik-v/yii2-date-range";a:3:{s:4:"name";s:24:"kartik-v/yii2-date-range";s:7:"version";s:7:"1.7.3.0";s:5:"alias";a:1:{s:17:"@kartik/daterange";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-date-range/src";}}s:22:"kartik-v/yii2-dynagrid";a:3:{s:4:"name";s:22:"kartik-v/yii2-dynagrid";s:7:"version";s:7:"1.5.5.0";s:5:"alias";a:1:{s:16:"@kartik/dynagrid";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-dynagrid/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:7:"2.0.7.0";s:5:"alias";a:1:{s:8:"@yii/jui";s:43:"C:\Web\Reclassering\vendor/yiisoft/yii2-jui";}}s:27:"kartik-v/yii2-label-inplace";a:3:{s:4:"name";s:27:"kartik-v/yii2-label-inplace";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/label";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-label-inplace/src";}}s:19:"kartik-v/yii2-icons";a:3:{s:4:"name";s:19:"kartik-v/yii2-icons";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/icons";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-icons/src";}}s:19:"kartik-v/yii2-money";a:3:{s:4:"name";s:19:"kartik-v/yii2-money";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/money";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-money/src";}}s:20:"kartik-v/yii2-ipinfo";a:3:{s:4:"name";s:20:"kartik-v/yii2-ipinfo";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/ipinfo";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-ipinfo/src";}}s:22:"kartik-v/yii2-markdown";a:3:{s:4:"name";s:22:"kartik-v/yii2-markdown";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/markdown";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-markdown/src";}}s:24:"kartik-v/yii2-dropdown-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-dropdown-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/dropdown";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-dropdown-x/src";}}s:19:"kartik-v/yii2-nav-x";a:3:{s:4:"name";s:19:"kartik-v/yii2-nav-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:11:"@kartik/nav";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-nav-x/src";}}s:22:"kartik-v/yii2-password";a:3:{s:4:"name";s:22:"kartik-v/yii2-password";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/password";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-password/src";}}s:20:"kartik-v/yii2-slider";a:3:{s:4:"name";s:20:"kartik-v/yii2-slider";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/slider";s:47:"C:\Web\Reclassering\vendor/kartik-v/yii2-slider";}}s:28:"kartik-v/yii2-sortable-input";a:3:{s:4:"name";s:28:"kartik-v/yii2-sortable-input";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:17:"@kartik/sortinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable-input/src";}}s:20:"kartik-v/yii2-tabs-x";a:3:{s:4:"name";s:20:"kartik-v/yii2-tabs-x";s:7:"version";s:7:"1.2.9.0";s:5:"alias";a:1:{s:12:"@kartik/tabs";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-tabs-x/src";}}s:30:"kartik-v/yii2-widget-typeahead";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-typeahead";s:7:"version";s:7:"1.0.4.0";s:5:"alias";a:1:{s:17:"@kartik/typeahead";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-typeahead/src";}}s:20:"kartik-v/yii2-social";a:3:{s:4:"name";s:20:"kartik-v/yii2-social";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:14:"@kartik/social";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-social/src";}}s:30:"kartik-v/yii2-widget-touchspin";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-touchspin";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:17:"@kartik/touchspin";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-touchspin/src";}}s:32:"kartik-v/yii2-widget-switchinput";a:3:{s:4:"name";s:32:"kartik-v/yii2-widget-switchinput";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:19:"@kartik/switchinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-switchinput";}}s:24:"kartik-v/yii2-validators";a:4:{s:4:"name";s:24:"kartik-v/yii2-validators";s:7:"version";s:7:"1.0.3.0";s:5:"alias";a:1:{s:18:"@kartik/validators";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-validators/src";}s:9:"bootstrap";s:27:"kartik\validators\Bootstrap";}s:28:"kartik-v/yii2-widget-spinner";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-spinner";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/spinner";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-spinner/src";}}s:28:"kartik-v/yii2-widget-sidenav";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-sidenav";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/sidenav";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-sidenav";}}s:27:"kartik-v/yii2-widget-rating";a:3:{s:4:"name";s:27:"kartik-v/yii2-widget-rating";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:14:"@kartik/rating";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rating/src";}}s:31:"kartik-v/yii2-widget-rangeinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-rangeinput";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:13:"@kartik/range";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rangeinput/src";}}s:26:"kartik-v/yii2-widget-growl";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-growl";s:7:"version";s:7:"1.1.2.0";s:5:"alias";a:1:{s:13:"@kartik/growl";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-growl/src";}}s:28:"kartik-v/yii2-widget-depdrop";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-depdrop";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:15:"@kartik/depdrop";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-depdrop/src";}}s:31:"kartik-v/yii2-widget-colorinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-colorinput";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:13:"@kartik/color";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-colorinput/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:7:"1.5.1.0";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:66:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:31:"kartik-v/yii2-widget-datepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-datepicker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:12:"@kartik/date";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datepicker/src";}}s:26:"kartik-v/yii2-widget-alert";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-alert";s:7:"version";s:7:"1.1.5.0";s:5:"alias";a:1:{s:13:"@kartik/alert";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-alert/src";}}s:26:"kartik-v/yii2-widget-affix";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-affix";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:13:"@kartik/affix";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-affix";}}s:21:"kartik-v/yii2-widgets";a:3:{s:4:"name";s:21:"kartik-v/yii2-widgets";s:7:"version";s:7:"3.4.1.0";s:5:"alias";a:1:{s:15:"@kartik/widgets";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-widgets/src";}}s:33:"kartik-v/yii2-bootstrap5-dropdown";a:3:{s:4:"name";s:33:"kartik-v/yii2-bootstrap5-dropdown";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:19:"@kartik/bs5dropdown";s:64:"C:\Web\Reclassering\vendor/kartik-v/yii2-bootstrap5-dropdown/src";}}s:26:"biladina/yii2-ajaxcrud-bs4";a:4:{s:4:"name";s:26:"biladina/yii2-ajaxcrud-bs4";s:7:"version";s:7:"3.0.0.0";s:5:"alias";a:1:{s:22:"@yii2ajaxcrud/ajaxcrud";s:57:"C:\Web\Reclassering\vendor/biladina/yii2-ajaxcrud-bs4/src";}s:9:"bootstrap";s:31:"yii2ajaxcrud\ajaxcrud\Bootstrap";}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@kartik/select2";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-select2/src";}}s:31:"kartik-v/yii2-widget-activeform";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-activeform";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/form";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-activeform/src";}}s:23:"raoul2000/yii2-workflow";a:3:{s:4:"name";s:23:"raoul2000/yii2-workflow";s:7:"version";s:7:"1.2.0.0";s:5:"alias";a:1:{s:19:"@raoul2000/workflow";s:54:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow/src";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:20:"kartik-v/yii2-export";a:3:{s:4:"name";s:20:"kartik-v/yii2-export";s:7:"version";s:7:"1.4.3.0";s:5:"alias";a:1:{s:14:"@kartik/export";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-export/src";}}s:28:"raoul2000/yii2-workflow-view";a:3:{s:4:"name";s:28:"raoul2000/yii2-workflow-view";s:7:"version";s:7:"0.0.2.0";s:5:"alias";a:1:{s:24:"@raoul2000/workflow/view";s:59:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow-view/src";}}s:32:"cornernote/yii2-workflow-manager";a:3:{s:4:"name";s:32:"cornernote/yii2-workflow-manager";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:28:"@cornernote/workflow/manager";s:63:"C:\Web\Reclassering\vendor/cornernote/yii2-workflow-manager/src";}}s:28:"2amigos/yii2-ckeditor-widget";a:3:{s:4:"name";s:28:"2amigos/yii2-ckeditor-widget";s:7:"version";s:7:"2.1.0.0";s:5:"alias";a:1:{s:19:"@dosamigos/ckeditor";s:59:"C:\Web\Reclassering\vendor/2amigos/yii2-ckeditor-widget/src";}}s:17:"yiisoft/yii2-twig";a:3:{s:4:"name";s:17:"yiisoft/yii2-twig";s:7:"version";s:7:"2.5.1.0";s:5:"alias";a:1:{s:9:"@yii/twig";s:48:"C:\Web\Reclassering\vendor/yiisoft/yii2-twig/src";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-debug/src";}}s:27:"2amigos/yii2-chartjs-widget";a:3:{s:4:"name";s:27:"2amigos/yii2-chartjs-widget";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:18:"@dosamigos/chartjs";s:58:"C:\Web\Reclassering\vendor/2amigos/yii2-chartjs-widget/src";}}s:19:"bedezign/yii2-audit";a:4:{s:4:"name";s:19:"bedezign/yii2-audit";s:7:"version";s:7:"1.2.7.0";s:5:"alias";a:1:{s:20:"@bedezign/yii2/audit";s:50:"C:\Web\Reclassering\vendor/bedezign/yii2-audit/src";}s:9:"bootstrap";s:29:"bedezign\yii2\audit\Bootstrap";}s:30:"kartik-v/yii2-widget-fileinput";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-fileinput";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/file";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-fileinput/src";}}s:24:"rmrevin/yii2-fontawesome";a:3:{s:4:"name";s:24:"rmrevin/yii2-fontawesome";s:7:"version";s:8:"2.10.3.0";s:5:"alias";a:1:{s:24:"@rmrevin/yii/fontawesome";s:51:"C:\Web\Reclassering\vendor/rmrevin/yii2-fontawesome";}}s:24:"edofre/yii2-fullcalendar";a:3:{s:4:"name";s:24:"edofre/yii2-fullcalendar";s:7:"version";s:8:"1.0.11.0";s:5:"alias";a:1:{s:20:"@edofre/fullcalendar";s:55:"C:\Web\Reclassering\vendor/edofre/yii2-fullcalendar/src";}}s:31:"kartik-v/yii2-widget-timepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-timepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/time";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-timepicker/src";}}s:34:"miloschuman/yii2-highcharts-widget";a:3:{s:4:"name";s:34:"miloschuman/yii2-highcharts-widget";s:7:"version";s:8:"11.0.0.0";s:5:"alias";a:1:{s:23:"@miloschuman/highcharts";s:65:"C:\Web\Reclassering\vendor/miloschuman/yii2-highcharts-widget/src";}}}}";s:3:"log";s:24727:"a:1:{s:8:"messages";a:49:{i:0;a:6:{i:0;s:55:"Bootstrap with kartik\validators\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.042275;i:4;a:0:{}i:5;i:3013920;}i:1;a:6:{i:0;s:59:"Bootstrap with yii2ajaxcrud\ajaxcrud\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.043224;i:4;a:0:{}i:5;i:3119112;}i:2;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.043232;i:4;a:0:{}i:5;i:3119408;}i:3;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.043586;i:4;a:0:{}i:5;i:3148768;}i:4;a:6:{i:0;s:57:"Bootstrap with bedezign\yii2\audit\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.043862;i:4;a:0:{}i:5;i:3176880;}i:5;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.044768;i:4;a:0:{}i:5;i:3397448;}i:6;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.044775;i:4;a:0:{}i:5;i:3398088;}i:7;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.049075;i:4;a:0:{}i:5;i:4297776;}i:8;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.056255;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5687408;}i:9;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.056283;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5689688;}i:14;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.062398;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5747504;}i:17;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.064357;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5768224;}i:20;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.078729;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6172504;}i:23;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.098129;i:4;a:0:{}i:5;i:6874992;}i:24;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.101714;i:4;a:0:{}i:5;i:6989552;}i:25;a:6:{i:0;s:21:"Loading module: audit";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.101752;i:4;a:0:{}i:5;i:6990192;}i:26;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.104181;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7060112;}i:29;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.106823;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7071744;}i:32;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.107833;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7071528;}i:35;a:6:{i:0;s:40:"Bootstrap with bedezign\yii2\audit\Audit";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.115875;i:4;a:0:{}i:5;i:7317456;}i:37;a:6:{i:0;s:28:"Route requested: 'pineapple'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:**********.11603;i:4;a:0:{}i:5;i:7316384;}i:38;a:6:{i:0;s:29:"Route to run: pineapple/index";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.118826;i:4;a:0:{}i:5;i:7376256;}i:39;a:6:{i:0;s:161:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('pineapple/index', '1', '::1', 0, 'GET', '2025-10-01 13:50:49')";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.129139;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7647504;}i:42;a:6:{i:0;s:69:"SELECT * FROM `notification_trigger` WHERE `route`='/pineapple/index'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.136762;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7722904;}i:45;a:6:{i:0;s:70:"Running action: backend\controllers\PineappleController::actionIndex()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:**********.137841;i:4;a:0:{}i:5;i:7717112;}i:46;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `test`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.141281;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\search\TestSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\backend\controllers\PineappleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:31:"common\models\search\TestSearch";s:4:"type";s:2:"->";}}i:5;i:7910112;}i:49;a:6:{i:0;s:24:"SHOW CREATE TABLE `test`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.143997;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\search\TestSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\backend\controllers\PineappleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:31:"common\models\search\TestSearch";s:4:"type";s:2:"->";}}i:5;i:7916512;}i:52;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'test' AND `kcu`.`TABLE_NAME` = 'test'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.14497;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\search\TestSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\backend\controllers\PineappleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:31:"common\models\search\TestSearch";s:4:"type";s:2:"->";}}i:5;i:7920720;}i:55;a:6:{i:0;s:74:"Rendering view file: C:\Web\Reclassering\backend\views\pineapple\index.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.148813;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\backend\controllers\PineappleController.php";s:4:"line";i:44;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:8375416;}i:56;a:6:{i:0;s:24:"Loading module: gridview";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.199831;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-krajee-base\src\Config.php";s:4:"line";i:313;s:8:"function";s:9:"getModule";s:5:"class";s:15:"yii\base\Module";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1255;s:8:"function";s:9:"getModule";s:5:"class";s:18:"kartik\base\Config";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:824;s:8:"function";s:10:"initModule";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:9770608;}i:57;a:6:{i:0;s:27:"SELECT COUNT(*) FROM `test`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.225186;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10531936;}i:60;a:6:{i:0;s:29:"SELECT * FROM `test` LIMIT 20";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.250255;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\pineapple\index.php";s:4:"line";i:23;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11116808;}i:63;a:6:{i:0;s:71:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\main.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.262738;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\backend\controllers\PineappleController.php";s:4:"line";i:44;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11308384;}i:64;a:6:{i:0;s:73:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\navbar.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.283789;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\backend\controllers\PineappleController.php";s:4:"line";i:44;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11418296;}i:65;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.287177;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11498648;}i:68;a:6:{i:0;s:107:"SELECT * FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0) ORDER BY `created_at` DESC LIMIT 10";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.289436;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11514152;}i:71;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.29033;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11525184;}i:74;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.291951;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11537200;}i:77;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.294395;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11543832;}i:80;a:6:{i:0;s:79:"Rendering view file: C:\Web\Reclassering\common\widgets\views\notifications.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.296363;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:28;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11554760;}i:81;a:6:{i:0;s:73:"Rendering view file: C:\Web\Reclassering\common\widgets\views\profile.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.296931;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\ProfileWidget.php";s:4:"line";i:32;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:26;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11562136;}i:82;a:6:{i:0;s:74:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\sidebar.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.297144;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:49;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\backend\controllers\PineappleController.php";s:4:"line";i:44;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11527424;}i:83;a:6:{i:0;s:73:"Rendering view file: C:\Web\Reclassering\common\widgets\views\sidebar.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.297406;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:49;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11573456;}i:84;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.298846;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11651608;}i:87;a:6:{i:0;s:74:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\content.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.305591;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:52;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\backend\controllers\PineappleController.php";s:4:"line";i:44;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11686456;}i:88;a:6:{i:0;s:82:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\control-sidebar.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.30901;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:56;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\backend\controllers\PineappleController.php";s:4:"line";i:44;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11808064;}i:89;a:6:{i:0;s:73:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\footer.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.309853;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\backend\controllers\PineappleController.php";s:4:"line";i:44;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11810384;}i:90;a:6:{i:0;s:69:"SELECT * FROM `notification_trigger` WHERE `route`='/pineapple/index'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.315476;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:44;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:11828416;}i:93;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.28163814544678, `memory_max`=12010648 WHERE `id`=5689";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.31682;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:11830344;}}}";s:9:"profiling";s:31684:"a:3:{s:6:"memory";i:12010648;s:4:"time";d:0.28926706314086914;s:8:"messages";a:46:{i:10;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.05629;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5690496;}i:11;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.059557;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5733800;}i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.059584;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5733584;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.062339;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5746216;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.062412;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5748416;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.062996;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5750992;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.064376;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5769264;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.066162;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5771792;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.078802;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6172888;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.079455;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6175256;}i:27;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.104246;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7061024;}i:28;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.106777;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7070456;}i:30;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.106837;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7072656;}i:31;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.107715;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7074568;}i:33;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.107846;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7073208;}i:34;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.109779;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7075064;}i:40;a:6:{i:0;s:161:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('pineapple/index', '1', '::1', 0, 'GET', '2025-10-01 13:50:49')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.129203;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7648864;}i:41;a:6:{i:0;s:161:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('pineapple/index', '1', '::1', 0, 'GET', '2025-10-01 13:50:49')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.134434;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7650656;}i:43;a:6:{i:0;s:69:"SELECT * FROM `notification_trigger` WHERE `route`='/pineapple/index'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.136822;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7724408;}i:44;a:6:{i:0;s:69:"SELECT * FROM `notification_trigger` WHERE `route`='/pineapple/index'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.137773;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7725888;}i:47;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `test`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.141385;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\search\TestSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\backend\controllers\PineappleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:31:"common\models\search\TestSearch";s:4:"type";s:2:"->";}}i:5;i:7911400;}i:48;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `test`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.143852;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\search\TestSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\backend\controllers\PineappleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:31:"common\models\search\TestSearch";s:4:"type";s:2:"->";}}i:5;i:7914848;}i:50;a:6:{i:0;s:24:"SHOW CREATE TABLE `test`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.144035;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\search\TestSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\backend\controllers\PineappleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:31:"common\models\search\TestSearch";s:4:"type";s:2:"->";}}i:5;i:7917800;}i:51;a:6:{i:0;s:24:"SHOW CREATE TABLE `test`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.14484;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\search\TestSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\backend\controllers\PineappleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:31:"common\models\search\TestSearch";s:4:"type";s:2:"->";}}i:5;i:7919664;}i:53;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'test' AND `kcu`.`TABLE_NAME` = 'test'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.14499;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\search\TestSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\backend\controllers\PineappleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:31:"common\models\search\TestSearch";s:4:"type";s:2:"->";}}i:5;i:7922136;}i:54;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'test' AND `kcu`.`TABLE_NAME` = 'test'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.147274;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\search\TestSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\backend\controllers\PineappleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:31:"common\models\search\TestSearch";s:4:"type";s:2:"->";}}i:5;i:7924368;}i:58;a:6:{i:0;s:27:"SELECT COUNT(*) FROM `test`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.225231;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10533600;}i:59;a:6:{i:0;s:27:"SELECT COUNT(*) FROM `test`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.227983;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10535224;}i:61;a:6:{i:0;s:29:"SELECT * FROM `test` LIMIT 20";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.25033;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\pineapple\index.php";s:4:"line";i:23;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11118472;}i:62;a:6:{i:0;s:29:"SELECT * FROM `test` LIMIT 20";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.251271;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\pineapple\index.php";s:4:"line";i:23;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11121520;}i:66;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.287246;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11500424;}i:67;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.288264;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11502120;}i:69;a:6:{i:0;s:107:"SELECT * FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0) ORDER BY `created_at` DESC LIMIT 10";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.289477;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11515928;}i:70;a:6:{i:0;s:107:"SELECT * FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0) ORDER BY `created_at` DESC LIMIT 10";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.29017;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11521080;}i:72;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.290371;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11526208;}i:73;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.291921;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11534512;}i:75;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.291961;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11538864;}i:76;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.292665;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11541664;}i:78;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.294422;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11544984;}i:79;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.296154;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11548832;}i:85;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.298912;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11653280;}i:86;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.300225;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11685896;}i:91;a:6:{i:0;s:69:"SELECT * FROM `notification_trigger` WHERE `route`='/pineapple/index'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.315532;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:44;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:11829600;}i:92;a:6:{i:0;s:69:"SELECT * FROM `notification_trigger` WHERE `route`='/pineapple/index'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.316547;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:44;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:11831080;}i:94;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.28163814544678, `memory_max`=12010648 WHERE `id`=5689";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.316842;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:11831688;}i:95;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.28163814544678, `memory_max`=12010648 WHERE `id`=5689";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.321113;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:11833088;}}}";s:2:"db";s:30914:"a:1:{s:8:"messages";a:44:{i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.059584;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5733584;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.062339;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5746216;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.062412;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5748416;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.062996;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5750992;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.064376;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5769264;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.066162;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5771792;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.078802;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6172888;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.079455;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6175256;}i:27;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.104246;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7061024;}i:28;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.106777;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7070456;}i:30;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.106837;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7072656;}i:31;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.107715;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7074568;}i:33;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.107846;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7073208;}i:34;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.109779;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7075064;}i:40;a:6:{i:0;s:161:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('pineapple/index', '1', '::1', 0, 'GET', '2025-10-01 13:50:49')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.129203;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7648864;}i:41;a:6:{i:0;s:161:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('pineapple/index', '1', '::1', 0, 'GET', '2025-10-01 13:50:49')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.134434;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7650656;}i:43;a:6:{i:0;s:69:"SELECT * FROM `notification_trigger` WHERE `route`='/pineapple/index'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.136822;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7724408;}i:44;a:6:{i:0;s:69:"SELECT * FROM `notification_trigger` WHERE `route`='/pineapple/index'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.137773;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7725888;}i:47;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `test`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.141385;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\search\TestSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\backend\controllers\PineappleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:31:"common\models\search\TestSearch";s:4:"type";s:2:"->";}}i:5;i:7911400;}i:48;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `test`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.143852;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\search\TestSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\backend\controllers\PineappleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:31:"common\models\search\TestSearch";s:4:"type";s:2:"->";}}i:5;i:7914848;}i:50;a:6:{i:0;s:24:"SHOW CREATE TABLE `test`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.144035;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\search\TestSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\backend\controllers\PineappleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:31:"common\models\search\TestSearch";s:4:"type";s:2:"->";}}i:5;i:7917800;}i:51;a:6:{i:0;s:24:"SHOW CREATE TABLE `test`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.14484;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\search\TestSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\backend\controllers\PineappleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:31:"common\models\search\TestSearch";s:4:"type";s:2:"->";}}i:5;i:7919664;}i:53;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'test' AND `kcu`.`TABLE_NAME` = 'test'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.14499;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\search\TestSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\backend\controllers\PineappleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:31:"common\models\search\TestSearch";s:4:"type";s:2:"->";}}i:5;i:7922136;}i:54;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'test' AND `kcu`.`TABLE_NAME` = 'test'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.147274;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\search\TestSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\backend\controllers\PineappleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:31:"common\models\search\TestSearch";s:4:"type";s:2:"->";}}i:5;i:7924368;}i:58;a:6:{i:0;s:27:"SELECT COUNT(*) FROM `test`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.225231;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10533600;}i:59;a:6:{i:0;s:27:"SELECT COUNT(*) FROM `test`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.227983;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10535224;}i:61;a:6:{i:0;s:29:"SELECT * FROM `test` LIMIT 20";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.25033;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\pineapple\index.php";s:4:"line";i:23;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11118472;}i:62;a:6:{i:0;s:29:"SELECT * FROM `test` LIMIT 20";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.251271;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\pineapple\index.php";s:4:"line";i:23;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11121520;}i:66;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.287246;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11500424;}i:67;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.288264;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11502120;}i:69;a:6:{i:0;s:107:"SELECT * FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0) ORDER BY `created_at` DESC LIMIT 10";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.289477;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11515928;}i:70;a:6:{i:0;s:107:"SELECT * FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0) ORDER BY `created_at` DESC LIMIT 10";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.29017;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11521080;}i:72;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.290371;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11526208;}i:73;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.291921;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11534512;}i:75;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.291961;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11538864;}i:76;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.292665;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11541664;}i:78;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.294422;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11544984;}i:79;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.296154;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:26;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11548832;}i:85;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.298912;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11653280;}i:86;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.300225;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11685896;}i:91;a:6:{i:0;s:69:"SELECT * FROM `notification_trigger` WHERE `route`='/pineapple/index'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.315532;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:44;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:11829600;}i:92;a:6:{i:0;s:69:"SELECT * FROM `notification_trigger` WHERE `route`='/pineapple/index'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.316547;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:44;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:11831080;}i:94;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.28163814544678, `memory_max`=12010648 WHERE `id`=5689";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.316842;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:11831688;}i:95;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.28163814544678, `memory_max`=12010648 WHERE `id`=5689";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.321113;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:11833088;}}}";s:5:"event";s:32015:"a:171:{i:0;a:5:{s:4:"time";d:**********.053353;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:**********.059548;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:**********.080299;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:3;a:5:{s:4:"time";d:**********.080427;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:4;a:5:{s:4:"time";d:**********.09155;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:5;a:5:{s:4:"time";d:**********.115973;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:6;a:5:{s:4:"time";d:**********.123305;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:7;a:5:{s:4:"time";d:**********.123376;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:8;a:5:{s:4:"time";d:**********.127437;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:9;a:5:{s:4:"time";d:**********.134868;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:10;a:5:{s:4:"time";d:**********.134884;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:11;a:5:{s:4:"time";d:**********.135247;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"backend\controllers\PineappleController";}i:12;a:5:{s:4:"time";d:**********.136519;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:13;a:5:{s:4:"time";d:**********.13852;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\search\TestSearch";}i:14;a:5:{s:4:"time";d:**********.138554;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:15;a:5:{s:4:"time";d:**********.139387;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\search\TestSearch";}i:16;a:5:{s:4:"time";d:**********.147321;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\search\TestSearch";}i:17;a:5:{s:4:"time";d:**********.148807;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:18;a:5:{s:4:"time";d:**********.187711;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"common\components\CreateNewButton";}i:19;a:5:{s:4:"time";d:**********.188256;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:20;a:5:{s:4:"time";d:**********.189013;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"common\components\CreateNewButton";}i:21;a:5:{s:4:"time";d:**********.194541;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"common\components\CreateNewButton";}i:22;a:5:{s:4:"time";d:**********.195859;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"yii2ajaxcrud\ajaxcrud\BulkButtonWidget";}i:23;a:5:{s:4:"time";d:**********.195885;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"yii2ajaxcrud\ajaxcrud\BulkButtonWidget";}i:24;a:5:{s:4:"time";d:**********.195904;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"yii2ajaxcrud\ajaxcrud\BulkButtonWidget";}i:25;a:5:{s:4:"time";d:**********.203982;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:26;a:5:{s:4:"time";d:**********.20414;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:27;a:5:{s:4:"time";d:**********.204229;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:28;a:5:{s:4:"time";d:**********.204334;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\grid\GridView";}i:29;a:5:{s:4:"time";d:**********.222984;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:30;a:5:{s:4:"time";d:**********.223113;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:31;a:5:{s:4:"time";d:**********.223153;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:32;a:5:{s:4:"time";d:**********.223186;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:33;a:5:{s:4:"time";d:**********.223278;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\grid\GridView";}i:34;a:5:{s:4:"time";d:**********.22817;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:35;a:5:{s:4:"time";d:**********.228202;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:36;a:5:{s:4:"time";d:**********.228207;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:37;a:5:{s:4:"time";d:**********.228211;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:38;a:5:{s:4:"time";d:**********.228215;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:39;a:5:{s:4:"time";d:**********.228233;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:40;a:5:{s:4:"time";d:**********.228237;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:41;a:5:{s:4:"time";d:**********.228244;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:42;a:5:{s:4:"time";d:**********.228248;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:43;a:5:{s:4:"time";d:**********.228251;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:44;a:5:{s:4:"time";d:**********.228255;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:45;a:5:{s:4:"time";d:**********.22826;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:46;a:5:{s:4:"time";d:**********.228267;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:47;a:5:{s:4:"time";d:**********.228271;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:48;a:5:{s:4:"time";d:**********.228276;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:49;a:5:{s:4:"time";d:**********.228279;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:50;a:5:{s:4:"time";d:**********.228283;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:51;a:5:{s:4:"time";d:**********.228294;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:52;a:5:{s:4:"time";d:**********.228299;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:53;a:5:{s:4:"time";d:**********.228303;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:54;a:5:{s:4:"time";d:**********.228306;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:55;a:5:{s:4:"time";d:**********.22831;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:56;a:5:{s:4:"time";d:**********.228314;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:57;a:5:{s:4:"time";d:**********.228322;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:58;a:5:{s:4:"time";d:**********.228326;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:59;a:5:{s:4:"time";d:**********.22833;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:60;a:5:{s:4:"time";d:**********.228334;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:61;a:5:{s:4:"time";d:**********.228338;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:62;a:5:{s:4:"time";d:**********.228342;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:63;a:5:{s:4:"time";d:**********.228346;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:64;a:5:{s:4:"time";d:**********.228349;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:65;a:5:{s:4:"time";d:**********.228353;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:66;a:5:{s:4:"time";d:**********.228357;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:67;a:5:{s:4:"time";d:**********.228361;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:68;a:5:{s:4:"time";d:**********.228364;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:69;a:5:{s:4:"time";d:**********.228369;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:70;a:5:{s:4:"time";d:**********.228372;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:71;a:5:{s:4:"time";d:**********.228376;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:72;a:5:{s:4:"time";d:**********.22838;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:73;a:5:{s:4:"time";d:**********.228384;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:74;a:5:{s:4:"time";d:**********.229683;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"kartik\bs5dropdown\ButtonDropdown";}i:75;a:5:{s:4:"time";d:**********.229703;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"kartik\bs5dropdown\ButtonDropdown";}i:76;a:5:{s:4:"time";d:**********.231023;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\bootstrap5\Button";}i:77;a:5:{s:4:"time";d:**********.231042;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\bootstrap5\Button";}i:78;a:5:{s:4:"time";d:**********.231091;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\bootstrap5\Button";}i:79;a:5:{s:4:"time";d:**********.233143;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"kartik\bs5dropdown\Dropdown";}i:80;a:5:{s:4:"time";d:**********.233199;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"kartik\bs5dropdown\Dropdown";}i:81;a:5:{s:4:"time";d:**********.236149;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"kartik\bs5dropdown\Dropdown";}i:82;a:5:{s:4:"time";d:**********.236209;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"kartik\bs5dropdown\ButtonDropdown";}i:83;a:5:{s:4:"time";d:**********.239905;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\dialog\Dialog";}i:84;a:5:{s:4:"time";d:**********.239955;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\dialog\Dialog";}i:85;a:5:{s:4:"time";d:**********.240999;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:86;a:5:{s:4:"time";d:**********.241043;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:87;a:5:{s:4:"time";d:**********.241074;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:88;a:5:{s:4:"time";d:**********.241112;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:89;a:5:{s:4:"time";d:**********.243927;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\dialog\Dialog";}i:90;a:5:{s:4:"time";d:**********.248188;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:91;a:5:{s:4:"time";d:**********.250069;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Test";}i:92;a:5:{s:4:"time";d:**********.251334;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Test";}i:93;a:5:{s:4:"time";d:**********.251403;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Test";}i:94;a:5:{s:4:"time";d:**********.251439;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Test";}i:95;a:5:{s:4:"time";d:**********.251474;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Test";}i:96;a:5:{s:4:"time";d:**********.251489;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Test";}i:97;a:5:{s:4:"time";d:**********.251503;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Test";}i:98;a:5:{s:4:"time";d:**********.251617;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:99;a:5:{s:4:"time";d:**********.255344;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:100;a:5:{s:4:"time";d:**********.257831;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\widgets\LinkPager";}i:101;a:5:{s:4:"time";d:**********.257859;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\widgets\LinkPager";}i:102;a:5:{s:4:"time";d:**********.257884;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\widgets\LinkPager";}i:103;a:5:{s:4:"time";d:**********.257979;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:104;a:5:{s:4:"time";d:**********.259243;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:105;a:5:{s:4:"time";d:**********.259262;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\grid\GridView";}i:106;a:5:{s:4:"time";d:**********.260812;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"yii\bootstrap5\Modal";}i:107;a:5:{s:4:"time";d:**********.261803;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"yii\bootstrap5\Modal";}i:108;a:5:{s:4:"time";d:**********.261948;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"yii\bootstrap5\Modal";}i:109;a:5:{s:4:"time";d:**********.262099;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:110;a:5:{s:4:"time";d:**********.262715;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:111;a:5:{s:4:"time";d:**********.283322;s:4:"name";s:9:"beginPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:112;a:5:{s:4:"time";d:**********.283528;s:4:"name";s:9:"beginBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:113;a:5:{s:4:"time";d:**********.283775;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:114;a:5:{s:4:"time";d:**********.28574;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"common\widgets\NotificationWidget";}i:115;a:5:{s:4:"time";d:**********.285766;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"common\widgets\NotificationWidget";}i:116;a:5:{s:4:"time";d:**********.286939;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:117;a:5:{s:4:"time";d:**********.288326;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:118;a:5:{s:4:"time";d:**********.290246;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:119;a:5:{s:4:"time";d:**********.296201;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:120;a:5:{s:4:"time";d:**********.296212;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:121;a:5:{s:4:"time";d:**********.296219;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:122;a:5:{s:4:"time";d:**********.296225;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:123;a:5:{s:4:"time";d:**********.29623;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:124;a:5:{s:4:"time";d:**********.296237;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:125;a:5:{s:4:"time";d:**********.296239;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:126;a:5:{s:4:"time";d:**********.296241;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:127;a:5:{s:4:"time";d:**********.296243;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:128;a:5:{s:4:"time";d:**********.296245;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:129;a:5:{s:4:"time";d:**********.296252;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:130;a:5:{s:4:"time";d:**********.29636;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:131;a:5:{s:4:"time";d:**********.29669;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:132;a:5:{s:4:"time";d:**********.296699;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"common\widgets\NotificationWidget";}i:133;a:5:{s:4:"time";d:**********.29688;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\ProfileWidget";}i:134;a:5:{s:4:"time";d:**********.296884;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\ProfileWidget";}i:135;a:5:{s:4:"time";d:**********.296929;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:136;a:5:{s:4:"time";d:**********.29709;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:137;a:5:{s:4:"time";d:**********.297095;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\ProfileWidget";}i:138;a:5:{s:4:"time";d:**********.297106;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:139;a:5:{s:4:"time";d:**********.297142;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:140;a:5:{s:4:"time";d:**********.297363;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\SidebarWidget";}i:141;a:5:{s:4:"time";d:**********.297367;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\SidebarWidget";}i:142;a:5:{s:4:"time";d:**********.297405;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:143;a:5:{s:4:"time";d:**********.297805;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:144;a:5:{s:4:"time";d:**********.303568;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"common\components\LucideSidebarMenuHelper";}i:145;a:5:{s:4:"time";d:**********.303614;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"common\components\LucideSidebarMenuHelper";}i:146;a:5:{s:4:"time";d:**********.305162;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"common\components\LucideSidebarMenuHelper";}i:147;a:5:{s:4:"time";d:**********.305223;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:148;a:5:{s:4:"time";d:**********.305235;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\SidebarWidget";}i:149;a:5:{s:4:"time";d:**********.305253;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:150;a:5:{s:4:"time";d:**********.305579;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:151;a:5:{s:4:"time";d:**********.308465;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"yii\bootstrap5\Breadcrumbs";}i:152;a:5:{s:4:"time";d:**********.308485;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"yii\bootstrap5\Breadcrumbs";}i:153;a:5:{s:4:"time";d:**********.308592;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"yii\bootstrap5\Breadcrumbs";}i:154;a:5:{s:4:"time";d:**********.308668;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:155;a:5:{s:4:"time";d:**********.308998;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:156;a:5:{s:4:"time";d:**********.309618;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:157;a:5:{s:4:"time";d:**********.309842;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:158;a:5:{s:4:"time";d:**********.310494;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:159;a:5:{s:4:"time";d:**********.312936;s:4:"name";s:7:"endBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:160;a:5:{s:4:"time";d:**********.314297;s:4:"name";s:7:"endPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:161;a:5:{s:4:"time";d:**********.315155;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:162;a:5:{s:4:"time";d:**********.315193;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"backend\controllers\PineappleController";}i:163;a:5:{s:4:"time";d:**********.315266;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:164;a:5:{s:4:"time";d:**********.316617;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:165;a:5:{s:4:"time";d:**********.316697;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:166;a:5:{s:4:"time";d:**********.321178;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:167;a:5:{s:4:"time";d:**********.321197;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:168;a:5:{s:4:"time";d:**********.321215;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:169;a:5:{s:4:"time";d:**********.322414;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:170;a:5:{s:4:"time";d:**********.32289;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:92:"a:3:{s:5:"start";d:**********.035014;s:3:"end";d:**********.325364;s:6:"memory";i:12010648;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:316:"a:3:{s:8:"messages";a:1:{i:36;a:6:{i:0;s:56:"Pretty URL not enabled. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.116001;i:4;a:0:{}i:5;i:7316488;}}s:5:"route";s:15:"pineapple/index";s:6:"action";s:54:"backend\controllers\PineappleController::actionIndex()";}";s:7:"request";s:10340:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:18:{s:12:"content-type";s:0:"";s:14:"content-length";s:1:"0";s:14:"sec-fetch-dest";s:8:"document";s:14:"sec-fetch-user";s:2:"?1";s:14:"sec-fetch-mode";s:8:"navigate";s:14:"sec-fetch-site";s:11:"same-origin";s:25:"upgrade-insecure-requests";s:1:"1";s:18:"sec-ch-ua-platform";s:9:""Windows"";s:16:"sec-ch-ua-mobile";s:2:"?0";s:9:"sec-ch-ua";s:65:""Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36";s:7:"referer";s:92:"http://localhost:8005/backoffice/index.php?r=menu-item&MenuItemSearch%5BglobalSearch%5D=test";s:4:"host";s:14:"localhost:8005";s:6:"cookie";s:730:"sidebar-collapse=false; _identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; advanced-backend-fmz=msg0jjcuulvdd1jt7jk7ci5n7e; _csrf-backend=d57d0e62f0673ea7a569a387faeff435b0c13df08d7a55c84305ac2d1addb4d7a%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22S2a1g0PIuanhWMPXYPKYURpQV9jYOFOl%22%3B%7D; advanced-frontend-fmz=jo5q5qv2iftg98198qcl309jku; _csrf-frontend=259d9d86ae292c1896d2cc241320258367f253196d2f1b700ea6863495c4247fa%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22CpIO9lvLZeIgesR8qinE-CtAmbpVr2W0%22%3B%7D";s:15:"accept-language";s:14:"en-US,en;q=0.9";s:15:"accept-encoding";s:23:"gzip, deflate, br, zstd";s:6:"accept";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:10:"connection";s:10:"keep-alive";}s:15:"responseHeaders";a:9:{s:12:"X-Powered-By";s:9:"PHP/8.3.3";s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"68dd23a9185ea";s:16:"X-Debug-Duration";s:3:"288";s:12:"X-Debug-Link";s:64:"/backoffice/index.php?r=debug%2Fdefault%2Fview&tag=68dd23a9185ea";s:10:"Set-Cookie";s:311:"_identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; expires=Fri, 31 Oct 2025 12:50:49 GMT; Max-Age=2592000; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:15:"pineapple/index";s:6:"action";s:54:"backend\controllers\PineappleController::actionIndex()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:104:{s:13:"_FCGI_X_PIPE_";s:53:"\\.\pipe\IISFCGI-cf782014-f08a-43e5-95ef-c7e266ab784a";s:5:"PHPRC";s:26:"C:\Program Files\PHP\v8.3\";s:21:"PHP_FCGI_MAX_REQUESTS";s:5:"10000";s:15:"ALLUSERSPROFILE";s:14:"C:\ProgramData";s:7:"APPDATA";s:56:"C:\WINDOWS\system32\config\systemprofile\AppData\Roaming";s:15:"APP_POOL_CONFIG";s:57:"C:\inetpub\temp\apppools\Reclassering\Reclassering.config";s:11:"APP_POOL_ID";s:12:"Reclassering";s:17:"ChocolateyInstall";s:25:"C:\ProgramData\chocolatey";s:18:"CommonProgramFiles";s:29:"C:\Program Files\Common Files";s:23:"CommonProgramFiles(x86)";s:35:"C:\Program Files (x86)\Common Files";s:18:"CommonProgramW6432";s:29:"C:\Program Files\Common Files";s:12:"COMPUTERNAME";s:10:"EGOV-L-046";s:7:"ComSpec";s:27:"C:\WINDOWS\system32\cmd.exe";s:10:"DriverData";s:38:"C:\Windows\System32\Drivers\DriverData";s:10:"IGCCSVC_DB";s:416:"AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg==";s:12:"LOCALAPPDATA";s:54:"C:\WINDOWS\system32\config\systemprofile\AppData\Local";s:20:"NUMBER_OF_PROCESSORS";s:2:"12";s:14:"OnlineServices";s:15:"Online Services";s:2:"OS";s:10:"Windows_NT";s:4:"Path";s:582:"C:\Python313\Scripts\;C:\Python313\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\PHP\v8.3;C:\ProgramData\ComposerSetup\bin;C:\ProgramData\ComposerSetup\bin\composer.bat;C:\Program Files (x86)\HP\HP OCR\DB_Lib\;C:\Program Files\HP\Common\HPDestPlgIn\;C:\Program Files (x86)\HP\Common\HPDestPlgIn\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\GitHub CLI\;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:12:"platformcode";s:2:"AN";s:22:"PROCESSOR_ARCHITECTURE";s:5:"AMD64";s:20:"PROCESSOR_IDENTIFIER";s:51:"Intel64 Family 6 Model 186 Stepping 3, GenuineIntel";s:15:"PROCESSOR_LEVEL";s:1:"6";s:18:"PROCESSOR_REVISION";s:4:"ba03";s:11:"ProgramData";s:14:"C:\ProgramData";s:12:"ProgramFiles";s:16:"C:\Program Files";s:17:"ProgramFiles(x86)";s:22:"C:\Program Files (x86)";s:12:"ProgramW6432";s:16:"C:\Program Files";s:12:"PSModulePath";s:93:"C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules";s:6:"PUBLIC";s:15:"C:\Users\<USER>\WINDOWS";s:4:"TEMP";s:15:"C:\WINDOWS\TEMP";s:3:"TMP";s:15:"C:\WINDOWS\TEMP";s:10:"USERDOMAIN";s:3:"GOV";s:8:"USERNAME";s:11:"EGOV-L-046$";s:11:"USERPROFILE";s:40:"C:\WINDOWS\system32\config\systemprofile";s:6:"windir";s:10:"C:\WINDOWS";s:17:"ZES_ENABLE_SYSMAN";s:1:"1";s:14:"ORIG_PATH_INFO";s:21:"/backoffice/index.php";s:3:"URL";s:21:"/backoffice/index.php";s:15:"SERVER_SOFTWARE";s:18:"Microsoft-IIS/10.0";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:18:"SERVER_PORT_SECURE";s:1:"0";s:11:"SERVER_PORT";s:4:"8005";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SCRIPT_NAME";s:21:"/backoffice/index.php";s:15:"SCRIPT_FILENAME";s:41:"C:\Web\Reclassering\backend\web\index.php";s:11:"REQUEST_URI";s:33:"/backoffice/index.php?r=pineapple";s:14:"REQUEST_METHOD";s:3:"GET";s:11:"REMOTE_USER";s:0:"";s:11:"REMOTE_PORT";s:5:"56785";s:11:"REMOTE_HOST";s:3:"::1";s:11:"REMOTE_ADDR";s:3:"::1";s:12:"QUERY_STRING";s:11:"r=pineapple";s:15:"PATH_TRANSLATED";s:41:"C:\Web\Reclassering\backend\web\index.php";s:10:"LOGON_USER";s:0:"";s:10:"LOCAL_ADDR";s:3:"::1";s:18:"INSTANCE_META_PATH";s:11:"/LM/W3SVC/2";s:13:"INSTANCE_NAME";s:12:"RECLASSERING";s:11:"INSTANCE_ID";s:1:"2";s:20:"HTTPS_SERVER_SUBJECT";s:0:"";s:19:"HTTPS_SERVER_ISSUER";s:0:"";s:19:"HTTPS_SECRETKEYSIZE";s:0:"";s:13:"HTTPS_KEYSIZE";s:0:"";s:5:"HTTPS";s:3:"off";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:13:"DOCUMENT_ROOT";s:32:"C:\Web\Reclassering\frontend\web";s:12:"CONTENT_TYPE";s:0:"";s:14:"CONTENT_LENGTH";s:1:"0";s:12:"CERT_SUBJECT";s:0:"";s:17:"CERT_SERIALNUMBER";s:0:"";s:11:"CERT_ISSUER";s:0:"";s:10:"CERT_FLAGS";s:0:"";s:11:"CERT_COOKIE";s:0:"";s:9:"AUTH_USER";s:0:"";s:13:"AUTH_PASSWORD";s:0:"";s:9:"AUTH_TYPE";s:0:"";s:18:"APPL_PHYSICAL_PATH";s:32:"C:\Web\Reclassering\backend\web\";s:12:"APPL_MD_PATH";s:27:"/LM/W3SVC/2/ROOT/backoffice";s:20:"IIS_UrlRewriteModule";s:13:"7,1,1993,2351";s:19:"HTTP_SEC_FETCH_DEST";s:8:"document";s:19:"HTTP_SEC_FETCH_USER";s:2:"?1";s:19:"HTTP_SEC_FETCH_MODE";s:8:"navigate";s:19:"HTTP_SEC_FETCH_SITE";s:11:"same-origin";s:30:"HTTP_UPGRADE_INSECURE_REQUESTS";s:1:"1";s:23:"HTTP_SEC_CH_UA_PLATFORM";s:9:""Windows"";s:21:"HTTP_SEC_CH_UA_MOBILE";s:2:"?0";s:14:"HTTP_SEC_CH_UA";s:65:""Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36";s:12:"HTTP_REFERER";s:92:"http://localhost:8005/backoffice/index.php?r=menu-item&MenuItemSearch%5BglobalSearch%5D=test";s:9:"HTTP_HOST";s:14:"localhost:8005";s:11:"HTTP_COOKIE";s:730:"sidebar-collapse=false; _identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; advanced-backend-fmz=msg0jjcuulvdd1jt7jk7ci5n7e; _csrf-backend=d57d0e62f0673ea7a569a387faeff435b0c13df08d7a55c84305ac2d1addb4d7a%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22S2a1g0PIuanhWMPXYPKYURpQV9jYOFOl%22%3B%7D; advanced-frontend-fmz=jo5q5qv2iftg98198qcl309jku; _csrf-frontend=259d9d86ae292c1896d2cc241320258367f253196d2f1b700ea6863495c4247fa%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22CpIO9lvLZeIgesR8qinE-CtAmbpVr2W0%22%3B%7D";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"en-US,en;q=0.9";s:20:"HTTP_ACCEPT_ENCODING";s:23:"gzip, deflate, br, zstd";s:11:"HTTP_ACCEPT";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:9:"FCGI_ROLE";s:9:"RESPONDER";s:8:"PHP_SELF";s:21:"/backoffice/index.php";s:18:"REQUEST_TIME_FLOAT";d:**********.023343;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:1:{s:1:"r";s:9:"pineapple";}s:4:"POST";a:0:{}s:6:"COOKIE";a:6:{s:16:"sidebar-collapse";s:5:"false";s:17:"_identity-backend";s:157:"39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba:2:{i:0;s:17:"_identity-backend";i:1;s:46:"[1,"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW",2592000]";}";s:20:"advanced-backend-fmz";s:26:"msg0jjcuulvdd1jt7jk7ci5n7e";s:13:"_csrf-backend";s:139:"d57d0e62f0673ea7a569a387faeff435b0c13df08d7a55c84305ac2d1addb4d7a:2:{i:0;s:13:"_csrf-backend";i:1;s:32:"S2a1g0PIuanhWMPXYPKYURpQV9jYOFOl";}";s:21:"advanced-frontend-fmz";s:26:"jo5q5qv2iftg98198qcl309jku";s:14:"_csrf-frontend";s:140:"259d9d86ae292c1896d2cc241320258367f253196d2f1b700ea6863495c4247fa:2:{i:0;s:14:"_csrf-frontend";i:1;s:32:"CpIO9lvLZeIgesR8qinE-CtAmbpVr2W0";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:3:{s:7:"__flash";a:0:{}s:4:"__id";i:1;s:9:"__authKey";s:32:"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW";}}";s:4:"user";s:1549:"a:5:{s:2:"id";i:1;s:8:"identity";a:12:{s:2:"id";s:1:"1";s:8:"username";s:11:"'SuperUser'";s:8:"auth_key";s:34:"'R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW'";s:13:"password_hash";s:62:"'$2y$13$uUSLwNI3so2bzilzzZdKJ.C1qmfyTdLRRT1XVP8jYO1c38iE6dfxS'";s:20:"password_reset_token";s:4:"null";s:5:"email";s:17:"'<EMAIL>'";s:6:"status";s:2:"10";s:10:"created_at";s:10:"1741788198";s:10:"updated_at";s:10:"1749661338";s:18:"verification_token";s:45:"'oZxacBEp5N7LZAqXc3s1haihOCLK8dLU_1741788198'";s:7:"role_id";s:1:"1";s:12:"access_token";s:66:"'CjYetJZp6PI5vAvKuqm6TDSC_2kYfe_LtOkrjMVtEo4IseTg3J-r-Gp3gMgH-pr7'";}s:10:"attributes";a:12:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:8:"Username";}i:2;a:2:{s:9:"attribute";s:8:"auth_key";s:5:"label";s:8:"Auth Key";}i:3;a:2:{s:9:"attribute";s:13:"password_hash";s:5:"label";s:8:"Password";}i:4;a:2:{s:9:"attribute";s:20:"password_reset_token";s:5:"label";s:20:"Password Reset Token";}i:5;a:2:{s:9:"attribute";s:5:"email";s:5:"label";s:5:"Email";}i:6;a:2:{s:9:"attribute";s:6:"status";s:5:"label";s:6:"Status";}i:7;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:8;a:2:{s:9:"attribute";s:10:"updated_at";s:5:"label";s:10:"Updated At";}i:9;a:2:{s:9:"attribute";s:18:"verification_token";s:5:"label";s:18:"Verification Token";}i:10;a:2:{s:9:"attribute";s:7:"role_id";s:5:"label";s:4:"Role";}i:11;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}}s:13:"rolesProvider";N;s:19:"permissionsProvider";N;}";s:5:"asset";s:14836:"a:27:{s:31:"yii2ajaxcrud\ajaxcrud\CrudAsset";a:9:{s:10:"sourcePath";s:64:"C:\Web\Reclassering\vendor\biladina\yii2-ajaxcrud-bs4\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\a6a94290";s:7:"baseUrl";s:27:"/backoffice/assets/a6a94290";s:7:"depends";a:5:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";i:2;s:35:"yii\bootstrap5\BootstrapPluginAsset";i:3;s:25:"kartik\grid\GridViewAsset";i:4;s:38:"yii2ajaxcrud\ajaxcrud\FontAwesomeAsset";}s:2:"js";a:2:{i:0;s:14:"ModalRemote.js";i:1;s:11:"ajaxcrud.js";}s:3:"css";a:1:{i:0;s:12:"ajaxcrud.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\web\YiiAsset";a:9:{s:10:"sourcePath";s:46:"C:\Web\Reclassering\vendor\yiisoft\yii2/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\ea09678e";s:7:"baseUrl";s:27:"/backoffice/assets/ea09678e";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;s:6:"yii.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:19:"yii\web\JqueryAsset";a:9:{s:10:"sourcePath";s:50:"C:\Web\Reclassering/vendor/bower-asset/jquery/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\8bc86ca8";s:7:"baseUrl";s:27:"/backoffice/assets/8bc86ca8";s:7:"depends";a:0:{}s:2:"js";a:1:{i:0;s:9:"jquery.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:29:"yii\bootstrap5\BootstrapAsset";a:9:{s:10:"sourcePath";s:53:"C:\Web\Reclassering/vendor/bower-asset/bootstrap/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\4ceb4c69";s:7:"baseUrl";s:27:"/backoffice/assets/4ceb4c69";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:17:"css/bootstrap.css";}s:9:"jsOptions";a:1:{s:8:"position";i:1;}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:35:"yii\bootstrap5\BootstrapPluginAsset";a:9:{s:10:"sourcePath";s:53:"C:\Web\Reclassering/vendor/bower-asset/bootstrap/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\4ceb4c69";s:7:"baseUrl";s:27:"/backoffice/assets/4ceb4c69";s:7:"depends";a:1:{i:0;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:22:"js/bootstrap.bundle.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:25:"kartik\grid\GridViewAsset";a:17:{s:10:"sourcePath";s:56:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\a9ee7db7";s:7:"baseUrl";s:27:"/backoffice/assets/a9ee7db7";s:7:"depends";a:4:{i:0;s:25:"kartik\dialog\DialogAsset";i:1;s:22:"yii\grid\GridViewAsset";i:2;s:16:"yii\web\YiiAsset";i:3;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:15:"css/kv-grid.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:25:"kartik\dialog\DialogAsset";a:17:{s:10:"sourcePath";s:58:"C:\Web\Reclassering\vendor\kartik-v\yii2-dialog\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\1f8ca467";s:7:"baseUrl";s:27:"/backoffice/assets/1f8ca467";s:7:"depends";a:1:{i:0;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:12:"js/dialog.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:1:{s:8:"position";i:1;}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:22:"yii\grid\GridViewAsset";a:9:{s:10:"sourcePath";s:46:"C:\Web\Reclassering\vendor\yiisoft\yii2/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\ea09678e";s:7:"baseUrl";s:27:"/backoffice/assets/ea09678e";s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:2:"js";a:1:{i:0;s:15:"yii.gridView.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:38:"yii2ajaxcrud\ajaxcrud\FontAwesomeAsset";a:9:{s:10:"sourcePath";s:51:"C:\Web\Reclassering/vendor/fortawesome/font-awesome";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\713ebcc9";s:7:"baseUrl";s:27:"/backoffice/assets/713ebcc9";s:7:"depends";a:0:{}s:2:"js";a:1:{i:0;s:13:"js/all.min.js";}s:3:"css";a:1:{i:0;s:15:"css/all.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:31:"kartik\grid\CheckboxColumnAsset";a:17:{s:10:"sourcePath";s:56:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\a9ee7db7";s:7:"baseUrl";s:27:"/backoffice/assets/a9ee7db7";s:7:"depends";a:3:{i:0;s:25:"kartik\grid\GridViewAsset";i:1;s:16:"yii\web\YiiAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:22:"js/kv-grid-checkbox.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:32:"kartik\bs5dropdown\DropdownAsset";a:17:{s:10:"sourcePath";s:71:"C:\Web\Reclassering\vendor\kartik-v\yii2-bootstrap5-dropdown\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\14348fa8";s:7:"baseUrl";s:27:"/backoffice/assets/14348fa8";s:7:"depends";a:3:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";i:2;s:35:"yii\bootstrap5\BootstrapPluginAsset";}s:2:"js";a:1:{i:0;s:14:"js/dropdown.js";}s:3:"css";a:1:{i:0;s:16:"css/dropdown.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:1;s:9:"bsVersion";s:3:"5.x";s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:34:"kartik\dialog\DialogBootstrapAsset";a:17:{s:10:"sourcePath";s:58:"C:\Web\Reclassering\vendor\kartik-v\yii2-dialog\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\1f8ca467";s:7:"baseUrl";s:27:"/backoffice/assets/1f8ca467";s:7:"depends";a:4:{i:0;s:25:"kartik\dialog\DialogAsset";i:1;s:16:"yii\web\YiiAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";i:3;s:35:"yii\bootstrap5\BootstrapPluginAsset";}s:2:"js";a:1:{i:0;s:22:"js/bootstrap-dialog.js";}s:3:"css";a:1:{i:0;s:28:"css/bootstrap-dialog-bs4.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:1;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:28:"kartik\dialog\DialogYiiAsset";a:17:{s:10:"sourcePath";s:58:"C:\Web\Reclassering\vendor\kartik-v\yii2-dialog\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\1f8ca467";s:7:"baseUrl";s:27:"/backoffice/assets/1f8ca467";s:7:"depends";a:3:{i:0;s:25:"kartik\dialog\DialogAsset";i:1;s:16:"yii\web\YiiAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:16:"js/dialog-yii.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:27:"kartik\grid\GridExportAsset";a:17:{s:10:"sourcePath";s:56:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\a9ee7db7";s:7:"baseUrl";s:27:"/backoffice/assets/a9ee7db7";s:7:"depends";a:3:{i:0;s:25:"kartik\dialog\DialogAsset";i:1;s:16:"yii\web\YiiAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:20:"js/kv-grid-export.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:34:"kartik\grid\GridResizeColumnsAsset";a:17:{s:10:"sourcePath";s:56:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\a9ee7db7";s:7:"baseUrl";s:27:"/backoffice/assets/a9ee7db7";s:7:"depends";a:3:{i:0;s:16:"yii\web\YiiAsset";i:1;s:25:"kartik\grid\GridViewAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:29:"js/jquery.resizableColumns.js";}s:3:"css";a:1:{i:0;s:31:"css/jquery.resizableColumns.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:21:"yii\widgets\PjaxAsset";a:9:{s:10:"sourcePath";s:48:"C:\Web\Reclassering/vendor/bower-asset/yii2-pjax";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\38002d64";s:7:"baseUrl";s:27:"/backoffice/assets/38002d64";s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:2:"js";a:1:{i:0;s:14:"jquery.pjax.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:41:"hail812\adminlte3\assets\FontAwesomeAsset";a:9:{s:10:"sourcePath";s:74:"C:\Web\Reclassering/vendor/almasaeed2010/adminlte/plugins/fontawesome-free";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\9a5ee6fe";s:7:"baseUrl";s:27:"/backoffice/assets/9a5ee6fe";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:15:"css/all.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:38:"hail812\adminlte3\assets\AdminLteAsset";a:9:{s:10:"sourcePath";s:54:"C:\Web\Reclassering/vendor/almasaeed2010/adminlte/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\f545690b";s:7:"baseUrl";s:27:"/backoffice/assets/f545690b";s:7:"depends";a:2:{i:0;s:34:"hail812\adminlte3\assets\BaseAsset";i:1;s:36:"hail812\adminlte3\assets\PluginAsset";}s:2:"js";a:1:{i:0;s:18:"js/adminlte.min.js";}s:3:"css";a:1:{i:0;s:20:"css/adminlte.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:34:"hail812\adminlte3\assets\BaseAsset";a:9:{s:10:"sourcePath";N;s:8:"basePath";N;s:7:"baseUrl";N;s:7:"depends";a:3:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap4\BootstrapAsset";i:2;s:35:"yii\bootstrap4\BootstrapPluginAsset";}s:2:"js";a:0:{}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:29:"yii\bootstrap4\BootstrapAsset";a:9:{s:10:"sourcePath";s:51:"C:\Web\Reclassering/vendor/npm-asset/bootstrap/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\75eeaf84";s:7:"baseUrl";s:27:"/backoffice/assets/75eeaf84";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:17:"css/bootstrap.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:35:"yii\bootstrap4\BootstrapPluginAsset";a:9:{s:10:"sourcePath";s:51:"C:\Web\Reclassering/vendor/npm-asset/bootstrap/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\75eeaf84";s:7:"baseUrl";s:27:"/backoffice/assets/75eeaf84";s:7:"depends";a:2:{i:0;s:19:"yii\web\JqueryAsset";i:1;s:29:"yii\bootstrap4\BootstrapAsset";}s:2:"js";a:1:{i:0;s:22:"js/bootstrap.bundle.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:36:"hail812\adminlte3\assets\PluginAsset";a:9:{s:10:"sourcePath";s:57:"C:\Web\Reclassering/vendor/almasaeed2010/adminlte/plugins";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\834b4d89";s:7:"baseUrl";s:27:"/backoffice/assets/834b4d89";s:7:"depends";a:1:{i:0;s:34:"hail812\adminlte3\assets\BaseAsset";}s:2:"js";a:0:{}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:46:"/backoffice/assets/e4a6bb80/control_sidebar.js";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:31:"C:\Web\Reclassering\backend\web";s:7:"baseUrl";s:0:"";s:7:"depends";a:1:{i:0;s:39:"\hail812\adminlte3\assets\AdminLteAsset";}s:2:"js";a:1:{i:0;a:1:{i:0;s:45:"backoffice/assets/e4a6bb80/control_sidebar.js";}}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:39:"\hail812\adminlte3\assets\AdminLteAsset";a:9:{s:10:"sourcePath";s:54:"C:\Web\Reclassering/vendor/almasaeed2010/adminlte/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\f545690b";s:7:"baseUrl";s:27:"/backoffice/assets/f545690b";s:7:"depends";a:2:{i:0;s:34:"hail812\adminlte3\assets\BaseAsset";i:1;s:36:"hail812\adminlte3\assets\PluginAsset";}s:2:"js";a:1:{i:0;s:18:"js/adminlte.min.js";}s:3:"css";a:1:{i:0;s:20:"css/adminlte.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:23:"backend\assets\AppAsset";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:31:"C:\Web\Reclassering\backend\web";s:7:"baseUrl";s:11:"/backoffice";s:7:"depends";a:7:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";i:2;s:19:"yii\web\JqueryAsset";i:3;s:16:"yii\jui\JuiAsset";i:4;s:29:"kartik\sortable\SortableAsset";i:5;s:38:"hail812\adminlte3\assets\AdminLteAsset";i:6;s:36:"hail812\adminlte3\assets\PluginAsset";}s:2:"js";a:3:{i:0;s:50:"https://unpkg.com/lucide@latest/dist/umd/lucide.js";i:1;s:10:"js/main.js";i:2;s:75:"https://cdn.jsdelivr.net/npm/sweetalert2@11.7.1/dist/sweetalert2.all.min.js";}s:3:"css";a:1:{i:0;s:12:"css/site.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\jui\JuiAsset";a:9:{s:10:"sourcePath";s:48:"C:\Web\Reclassering/vendor/bower-asset/jquery-ui";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\a78fe255";s:7:"baseUrl";s:27:"/backoffice/assets/a78fe255";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;s:12:"jquery-ui.js";}s:3:"css";a:1:{i:0;s:31:"themes/smoothness/jquery-ui.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:29:"kartik\sortable\SortableAsset";a:17:{s:10:"sourcePath";s:60:"C:\Web\Reclassering\vendor\kartik-v\yii2-sortable\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\3202e5a2";s:7:"baseUrl";s:27:"/backoffice/assets/3202e5a2";s:7:"depends";a:2:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:2:{i:0;s:19:"js/html5sortable.js";i:1;s:23:"js/kv-html5-sortable.js";}s:3:"css";a:1:{i:0;s:25:"css/kv-html5-sortable.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}}";s:7:"summary";a:13:{s:3:"tag";s:13:"68dd23a9185ea";s:3:"url";s:54:"http://localhost:8005/backoffice/index.php?r=pineapple";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:3:"::1";s:4:"time";d:**********.023343;s:10:"statusCode";i:200;s:8:"sqlCount";i:22;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:12010648;s:14:"processingTime";d:0.28926706314086914;}s:10:"exceptions";a:0:{}}