<?php

use common\components\CreateNewButton;
use common\widgets\RecordSearchWidget;
use yii\helpers\Url;
use yii\helpers\Html;
use yii\bootstrap5\Modal;
use kartik\grid\GridView;
use yii2ajaxcrud\ajaxcrud\CrudAsset;
use yii2ajaxcrud\ajaxcrud\BulkButtonWidget;

/* @var $this yii\web\View */
/* @var $searchModel common\models\search\RapportSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Documenten';
// $this->params['breadcrumbs'][] = $this->title;

CrudAsset::register($this);

?>
<div class="rapport-index">
    <div id="ajaxCrudDatatable">
        <?= GridView::widget([
            'id' => 'crud-datatable',
            'dataProvider' => $dataProvider,
            // 'filterModel' => $searchModel,
            'pjax' => true,
            'columns' => require(__DIR__ . '/_columns.php'),
            'toolbar' => [
                [
                    'content' => CreateNewButton::widget([]) .
                        Html::a(
                            '<i class="fa fa-redo"></i>',
                            [''],
                            ['data-pjax' => 1, 'class' => 'btn btn-outline-success', 'title' => Yii::t('yii2-ajaxcrud', 'Reset Grid')]
                        ) . '{toggleData}' .
                        '{export}'
                ],
            ],
            'striped' => false,
            'hover' => true,
            'condensed' => true,
            'responsive' => true,
            'panel' => [
                'type' => 'default',
                'heading' => '<i class="fa fa-list"></i> <b>' . $this->title . '</b>',
                'before' => RecordSearchWidget::widget([
                    'id' => 'document-search',
                    'placeholder' => 'Search...',
                    'width' => 'w-50',
                    'paramName' => 'globalSearch'  // This will be converted to FunctionalitySearch[globalSearch]
                ]),
                'after' => BulkButtonWidget::widget([
                    'buttons' => (canBulkDeleteDocuments() ?
                        Html::a(
                            '<i class="fa fa-trash"></i>&nbsp; ' . Yii::t('yii2-ajaxcrud', 'Delete All'),
                            ["bulkdelete"],
                            [
                                'class' => 'btn btn-danger btn-xs',
                                'role' => 'modal-remote-bulk',
                                'data-confirm' => false,
                                'data-method' => false, // for overide yii data api
                                'data-request-method' => 'post',
                                'data-confirm-title' => Yii::t('yii2-ajaxcrud', 'Delete'),
                                'data-confirm-message' => Yii::t('yii2-ajaxcrud', 'Delete Confirm')
                            ]
                        ) : '') . (isAdminOrSuperAdmin() ?  Html::a(
                        '<i data-lucide="file-question-mark"></i> Get Document',
                        'http://localhost:8005/backoffice/index.php?r=document/get-document&id=1',
                        ['class' => 'btn btn-outline-dark btn-sm ms-2', 'target' => '_blank']

                    )  : ''),
                ]) .
                    '<div class="clearfix"></div>',
            ]
        ]) ?>
    </div>
</div>
<?php Modal::begin([
    "id" => "ajaxCrudModal",
    "footer" => "", // always need it for jquery plugin
    'size' => Modal::SIZE_LARGE,
    "clientOptions" => [
        "tabindex" => false,
        "backdrop" => "static",
        "keyboard" => false,
    ],
    "options" => [
        "tabindex" => false
    ]
]) ?>
<?php Modal::end(); ?>