<?php

if (!function_exists("canUploadDocuments")) {
    function canUploadDocuments()
    {
        return Yii::$app->accessHelper->canAccess('/document-upload/upload');
    }
}

if (!function_exists("canDeleteUploadedDocuments")) {
    function canDeleteUploadedDocuments()
    {
        return Yii::$app->accessHelper->canAccess('/document-upload/delete');
    }
}

if (!function_exists("canBulkDeleteDocuments")) {
    function canBulkDeleteDocuments()
    {
        return Yii::$app->accessHelper->canAccess('/document/bulkdelete');
    }
}

if (!function_exists("canManageUploadedDocuments")) {
    function canManageUploadedDocuments()
    {
        return Yii::$app->accessHelper->canAccess('/document-upload/manage-uploaded-documents');
    }
}




// Role based
if (!function_exists("isAdminOrSuperAdmin")) {
    function isAdminOrSuperAdmin()
    {
        if (Yii::$app->user->isGuest) {
            return false;
        }

        $user = Yii::$app->user->identity;
        return $user->role_id && in_array($user->role_id, [1, 2]); // 1 = superUser, 2 = admin
    }
}

if (!function_exists("isHoofdOrOnderhoofd")) {
    function isHoofdOrOnderhoofd()
    {
        if (Yii::$app->user->isGuest) {
            return false;
        }

        $user = Yii::$app->user->identity;
        return $user->role_id && in_array($user->role_id, [1, 6]); // 1 = superUser,  6 = hoofd
    }
}
