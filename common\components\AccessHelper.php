<?php

namespace common\components;

use common\models\DocumentUpload;
use Yii;
use yii\base\Component;
use common\models\RoleFunctionality;
use common\models\UserPermission;

class AccessHelper extends Component
{
    private static function getCurrentUser()
    {
        return Yii::$app->user->identity ?? null;
    }

    /**
     * Check if user can access a given route.
     */
    public function canAccess($route)
    {
        $user = self::getCurrentUser();
        if (!$user) {
            return false;
        }

        // 🔹 Super admin (role_id = 1) bypasses all checks
        if ((int)$user->role_id === 1) {
            return true;
        }

        // 1. Load permissions from cache (or build if not cached)
        $permissions = $this->getCachedPermissions($user->id, $user->role_id);

        // 2. Return access result for this route
        return $permissions[$route] ?? false;
    }

    /**
     * Build action template for GridView buttons.
     */
    public static function buildActionTemplate()
    {
        $user = self::getCurrentUser();
        if (!$user) {
            return '';
        }

        $permissions = Yii::$app->accessHelper->getCachedPermissions($user->id, $user->role_id);

        $controller = Yii::$app->controller->id;
        $template = '';

        foreach ($permissions as $route => $allowed) {
            if ($allowed) {
                // Extract action from route
                $action = str_replace('/', '', str_replace($controller . '/', '', $route));
                $template .= "{" . $action . "} ";
            }
        }

        return trim($template);
    }

    /**
     * Retrieve cached permissions or build them if not cached.
     */
    private function getCachedPermissions($userId, $roleId)
    {
        $cacheKey = "user_permissions_{$userId}";
        $cache = Yii::$app->cache;

        $permissions = $cache->get($cacheKey);
        if ($permissions !== false) {
            return $permissions;
        }

        // 🔹 Build permission map (user overrides > role defaults)
        $permissions = [];

        // 1. Role-based permissions
        $roleRoutes = RoleFunctionality::find()
            ->select('route')
            ->where(['role_id' => $roleId, 'active' => 1])
            ->asArray()
            ->column();

        foreach ($roleRoutes as $route) {
            $permissions[$route] = true;
        }

        // 2. User-specific overrides
        $userPermissions = UserPermission::find()
            ->select(['route', 'can_access'])
            ->where(['user_id' => $userId])
            ->asArray()
            ->all();

        foreach ($userPermissions as $perm) {
            $permissions[$perm['route']] = (bool)$perm['can_access'];
        }

        // Save in cache for 30 minutes
        $cache->set($cacheKey, $permissions, 3600);

        return $permissions;
    }

    public function canAccessDocument($documentId, $action = 'view')
    {
        $document = DocumentUpload::findOne($documentId);
        if (!$document) return false;

        return $document->userHasAccess();
    }
}
