<?php

use yii\helpers\Html;
use yii\helpers\Url;

/** @var integer $unreadCount */
/** @var \common\models\UserNotification[] $notifications */

$itemsHtml = '';
foreach ($notifications as $notif) {
    $itemClass = $notif->is_read ? 'text-muted' : 'fw-bold';
    $itemsHtml .= Html::a(
        Html::encode($notif->message),
        'javascript:void(0);',
        [
            'class' => "dropdown-item notification-link {$itemClass}",
            'style' => 'white-space: normal;',
            'data-id' => $notif->id,
            'data-link' => Url::to([$notif->link]),
        ]
    );
}

// Dropdown HTML
echo Html::tag(
    'li',
    Html::a(
        '<i data-lucide="bell-ring" class="align-middle" style="width: 18px; height: 18px;"></i>' . ($unreadCount ? " <span class='badge badge-danger navbar-badge'>{$unreadCount}</span>" : ''),
        '#',
        [
            'class' => 'nav-link',
            'data-toggle' => 'dropdown',
        ]
    ) .
        Html::tag('div', $itemsHtml ?: '<div class="dropdown-item text-muted">No notifications</div>', [
            'class' => 'dropdown-menu dropdown-menu-lg dropdown-menu-right',
            'style' => 'max-height: 300px; overflow-y: auto; width: 300px;',
        ]),
    ['class' => 'nav-item dropdown']
);
