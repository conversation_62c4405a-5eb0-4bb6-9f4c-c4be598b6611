<?php

namespace common\widgets;

use Yii;
use yii\base\Widget;
use common\models\NotificationUser;

class NotificationWidget extends Widget
{
    public $limit = 10;

    public function run()
    {
        $userId = Yii::$app->user->id;

        $unreadCount = NotificationUser::find()
            ->where(['user_id' => $userId, 'is_read' => 0])
            ->count();

        $notifications = NotificationUser::find()
            ->where(['user_id' => $userId])
            ->andWhere(['is_read' => 0])
            ->orderBy(['created_at' => SORT_DESC])
            ->limit($this->limit)
            ->all();

        return $this->render('notifications', [
            'unreadCount' => $unreadCount,
            'notifications' => $notifications,
        ]);
    }
}
