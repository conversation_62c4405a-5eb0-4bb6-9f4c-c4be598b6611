<?php

namespace backend\controllers;

use common\components\EncryptDecryptID;
use Yii;
use common\models\DocumentUpload;
use common\models\DocumentPermission;
use common\models\search\DocumentPermissionSearch;
use common\models\search\DocumentUploadSearch;
use yii\data\ActiveDataProvider;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use \yii\web\Response;
use yii\helpers\Html;
use yii\web\UploadedFile;

/**
 * DocumentUploadController implements the CRUD actions for DocumentUpload model.
 */
class DocumentUploadController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['post'],
                    'bulkdelete' => ['post'],
                    'ajax-upload' => ['post'], // <-- add this
                ],
            ],
        ];
    }

    /**
     * Lists all DocumentUpload models.
     * @return mixed
     */
    public function actionIndex()
    {
        $user = Yii::$app->user->identity;
        $query = DocumentUpload::find();

        // Case 1: instantie == 1 → see all
        if ($user->role->instantie->id !== 1) {
            $query->joinWith('documentPermissions dp')
                ->andWhere(['dp.user_id' => $user->id, 'dp.can_view' => 1]);
        }

        $dataProvider = new ActiveDataProvider([
            'query' => $query->distinct(),
            'pagination' => [
                'pageSize' => 20,
            ],
        ]);

        return $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }



    /**
     * Displays a single DocumentUpload model.
     * @param integer $id
     * @return mixed
     */
    // public function actionView($id)
    // {   
    //     $request = Yii::$app->request;
    //     if($request->isAjax)
    //     {
    //         Yii::$app->response->format = Response::FORMAT_JSON;
    //         return [
    //             'title' => "DocumentUpload #".$id,
    //             'content' =>$this->renderAjax('view', [
    //                 'model' => $this->findModel($id),
    //             ]),
    //             'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']).
    //                 Html::a(Yii::t('yii2-ajaxcrud', 'Update'), ['update', 'id' => $id], ['class' => 'btn btn-primary', 'role' => 'modal-remote'])
    //         ];
    //     }
    //     else
    //     {
    //         return $this->render('view', [
    //             'model' => $this->findModel($id),
    //         ]);
    //     }
    // }

    /**
     * Creates a new DocumentUpload model.
     * For ajax request will return json object
     * and for non-ajax request if creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $request = Yii::$app->request;
        $model = new DocumentUpload();

        if ($request->isAjax) {
            /*
            *   Process for ajax request
            */
            Yii::$app->response->format = Response::FORMAT_JSON;
            if ($request->isGet) {
                return [
                    'title' => Yii::t('yii2-ajaxcrud', 'Create New') . " DocumentUpload",
                    'content' => $this->renderAjax('create', [
                        'model' => $model,
                    ]),
                    'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                        Html::button(Yii::t('yii2-ajaxcrud', 'Create'), ['class' => 'btn btn-primary', 'type' => 'submit'])
                ];
            } else if ($model->load($request->post()) && $model->save()) {
                return [
                    'forceReload' => '#crud-datatable-pjax',
                    'title' => Yii::t('yii2-ajaxcrud', 'Create New') . " DocumentUpload",
                    'content' => '<span class="text-success">' . Yii::t('yii2-ajaxcrud', 'Create') . ' DocumentUpload ' . Yii::t('yii2-ajaxcrud', 'Success') . '</span>',
                    'footer' =>  Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                        Html::a(Yii::t('yii2-ajaxcrud', 'Create More'), ['create'], ['class' => 'btn btn-primary', 'role' => 'modal-remote'])
                ];
            } else {
                return [
                    'title' => Yii::t('yii2-ajaxcrud', 'Create New') . " DocumentUpload",
                    'content' => $this->renderAjax('create', [
                        'model' => $model,
                    ]),
                    'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                        Html::button(Yii::t('yii2-ajaxcrud', 'Save'), ['class' => 'btn btn-primary', 'type' => 'submit'])
                ];
            }
        } else {
            /*
            *   Process for non-ajax request
            */
            if ($model->load($request->post()) && $model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            } else {
                return $this->render('create', [
                    'model' => $model,
                ]);
            }
        }
    }

    /**
     * Updates an existing DocumentUpload model.
     * For ajax request will return json object
     * and for non-ajax request if update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $request = Yii::$app->request;
        $model = $this->findModel($id);

        if ($request->isAjax) {
            /*
            *   Process for ajax request
            */
            Yii::$app->response->format = Response::FORMAT_JSON;
            if ($request->isGet) {
                return [
                    'title' => Yii::t('yii2-ajaxcrud', 'Update') . " DocumentUpload #" . $id,
                    'content' => $this->renderAjax('update', [
                        'model' => $model,
                    ]),
                    'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                        Html::button(Yii::t('yii2-ajaxcrud', 'Save'), ['class' => 'btn btn-primary', 'type' => 'submit'])
                ];
            } else if ($model->load($request->post()) && $model->save()) {
                return [
                    'forceReload' => '#crud-datatable-pjax',
                    'title' => "DocumentUpload #" . $id,
                    'content' => $this->renderAjax('view', [
                        'model' => $model,
                    ]),
                    'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                        Html::a(Yii::t('yii2-ajaxcrud', 'Update'), ['update', 'id' => $id], ['class' => 'btn btn-primary', 'role' => 'modal-remote'])
                ];
            } else {
                return [
                    'title' => Yii::t('yii2-ajaxcrud', 'Update') . " DocumentUpload #" . $id,
                    'content' => $this->renderAjax('update', [
                        'model' => $model,
                    ]),
                    'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                        Html::button(Yii::t('yii2-ajaxcrud', 'Save'), ['class' => 'btn btn-primary', 'type' => 'submit'])
                ];
            }
        } else {
            /*
            *   Process for non-ajax request
            */
            if ($model->load($request->post()) && $model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            } else {
                return $this->render('update', [
                    'model' => $model,
                ]);
            }
        }
    }

    /**
     * Delete an existing DocumentUpload model.
     * For ajax request will return json object
     * and for non-ajax request if deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    // public function actionDelete($id)
    // {
    //     $request = Yii::$app->request;
    //     $this->findModel($id)->delete();

    //     if ($request->isAjax) {
    //         /*
    //         *   Process for ajax request
    //         */
    //         Yii::$app->response->format = Response::FORMAT_JSON;
    //         return ['forceClose' => true, 'forceReload' => '#crud-datatable-pjax'];
    //     } else {
    //         /*
    //         *   Process for non-ajax request
    //         */
    //         return $this->redirect(['index']);
    //     }
    // }

    /**
     * Delete multiple existing DocumentUpload model.
     * For ajax request will return json object
     * and for non-ajax request if deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionBulkdelete()
    {
        $request = Yii::$app->request;
        $pks = explode(',', $request->post('pks')); // Array or selected records primary keys
        foreach ($pks as $pk) {
            $model = $this->findModel($pk);
            $model->delete();
        }

        if ($request->isAjax) {
            /*
            *   Process for ajax request
            */
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ['forceClose' => true, 'forceReload' => '#crud-datatable-pjax'];
        } else {
            /*
            *   Process for non-ajax request
            */
            return $this->redirect(['index']);
        }
    }

    /**
     * Finds the DocumentUpload model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return DocumentUpload the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = DocumentUpload::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function actionUpload()
    {
        $model = new DocumentUpload();

        if (Yii::$app->request->isPost) {
            $customNames = Yii::$app->request->post('custom_names', []);
            $files = UploadedFile::getInstances($model, 'upload_file');
            $userId = Yii::$app->user->id;

            $publicPath = Yii::getAlias('@frontend/web/uploads/documents/');
            if (!file_exists($publicPath)) {
                mkdir($publicPath, 0775, true);
            }

            foreach ($files as $index => $file) {
                $customBaseName = $customNames[$index] ?? $file->baseName;
                $customFileName = 'uploaded_' . preg_replace('/[^A-Za-z0-9_\-]/', '_', $customBaseName) . '.' . $file->extension;
                $fullPath = $publicPath . $customFileName;
                $relativePath = 'uploads/documents/' . $customFileName;

                if ($file->saveAs($fullPath)) {
                    chmod($fullPath, 0644);
                    $upload = new DocumentUpload();
                    $upload->user_id = $userId;
                    $upload->upload_file = $file;
                    $upload->original_filename = $file->name;
                    $upload->filename = $customFileName;
                    $upload->file_path = $relativePath;
                    $upload->file_type = $file->type;
                    $upload->file_size = $file->size;
                    $upload->created_at = time();
                    $upload->updated_at = time();
                    $upload->save(false); // You can use validate() if needed
                }
            }

            Yii::$app->session->setFlash('success', 'Documents uploaded successfully.');
            return $this->redirect(['index']);
        }

        return $this->render('upload', [
            'model' => $model,
        ]);
    }

    public function actionView($id)
    {
        $model = DocumentUpload::findOne($id);
        if (!$model) {
            throw new NotFoundHttpException('Document not found.');
        }

        return $this->render('view', ['model' => $model]);
    }


    // public function actionServeDocument($id)
    // {
    //     try {
    //         $decryptedId = EncryptDecryptID::decryptId($id);
    //     } catch (\Exception $e) {
    //         throw new NotFoundHttpException('Invalid document ID.');
    //     }

    //     $document = DocumentUpload::findOne($decryptedId);

    //     if (!$document) {
    //         throw new NotFoundHttpException('Document not found.');
    //     }

    //     $publicPath = Yii::getAlias('@frontend/web/') . $document->file_path;
    //     $filename = $document->original_filename;

    //     if (!file_exists($publicPath)) {
    //         throw new NotFoundHttpException('File not found on server.');
    //     }

    //     // Stream the file
    //     return Yii::$app->response->sendFile(
    //         $publicPath,
    //         $filename,
    //         ['inline' => true]
    //     );
    // }

    public function actionServeDocument($id)
    {
        try {
            $decryptedId = EncryptDecryptID::decryptId($id);
        } catch (\Exception $e) {
            throw new NotFoundHttpException('Invalid document ID.');
        }

        $document = DocumentUpload::findOne($decryptedId);

        if (!$document) {
            throw new NotFoundHttpException('Document not found.');
        }

        // 🔹 Check access
        if (!$document->userHasAccess()) {
            throw new \yii\web\ForbiddenHttpException('You are not allowed to access this document.');
        }

        $publicPath = Yii::getAlias('@frontend/web/') . $document->file_path;
        $filename = $document->original_filename;

        if (!file_exists($publicPath)) {
            throw new NotFoundHttpException('File not found on server.');
        }

        return Yii::$app->response->sendFile($publicPath, $filename, ['inline' => true]);
    }


    public function actionDelete($id)
    {
        try {
            $decryptedId = EncryptDecryptID::decryptId($id);
        } catch (\Exception $e) {
            throw new NotFoundHttpException('Invalid document ID.');
        }

        $model = $this->findModel($decryptedId);
        $filePath = Yii::getAlias('@frontend/web/') . $model->file_path;

        // Delete file from filesystem
        if (file_exists($filePath)) {
            unlink($filePath);
        }

        // Delete record from database
        $model->delete();

        if (Yii::$app->request->isAjax) {
            Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
            return ['success' => true];
        }

        return $this->redirect(['index']);
    }

    public function actionManageUploadedDocuments()
    {
        $request = Yii::$app->request;

        if ($request->isPost && $request->post('document_ids')) {
            $documentIds = $request->post('document_ids', []);
            $permissions = $request->post('permissions', []);

            if (!empty($documentIds)) {
                // Delete existing permissions ONLY for selected docs + users
                DocumentPermission::deleteAll([
                    'document_id' => $documentIds,
                    'user_id' => array_keys($permissions)
                ]);

                // Bulk insert new permissions
                $rows = [];
                $time = time();
                foreach ($documentIds as $documentId) {
                    foreach ($permissions as $userId => $perms) {
                        if (isset($perms['can_view']) && $perms['can_view']) {
                            $rows[] = [
                                'document_id' => $documentId,
                                'user_id' => $userId,
                                'can_view' => 1,
                                'created_at' => $time,
                                'updated_at' => $time,
                            ];
                        }
                    }
                }

                if (!empty($rows)) {
                    Yii::$app->db->createCommand()
                        ->batchInsert(
                            DocumentPermission::tableName(),
                            ['document_id', 'user_id', 'can_view', 'created_at', 'updated_at'],
                            $rows
                        )
                        ->execute();
                }

                $documentCount = count($documentIds);
                Yii::$app->session->setFlash('success', "Permissions updated successfully for {$documentCount} document(s).");
                return $this->redirect(['manage-uploaded-documents']);
            }
        }

        // Data for the gridview
        $searchModel = new DocumentPermissionSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('manage-uploaded-documents', [
            'uploadedDocuments' => \common\models\DocumentUpload::find()->all(),
            'users' => \common\models\User::find()->all(),
            'permissions' => \common\models\DocumentPermission::find()->all(),
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }


    public function actionGetPermissions()
    {
        $request = Yii::$app->request;

        if ($request->isPost && $request->post('document_id')) {
            $documentId = $request->post('document_id');
            $permissions = DocumentPermission::find()
                ->where(['document_id' => $documentId])
                ->asArray()
                ->all();

            Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
            return $permissions;
        }

        return [];
    }
}
