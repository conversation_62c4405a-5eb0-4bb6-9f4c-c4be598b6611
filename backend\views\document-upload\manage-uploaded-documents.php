<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

$this->title = 'Manage Document Permissions';
?>

<div class="manage-documents py-2">
    <!-- Card for assigning view permissions -->
    <div class="card rounded-3 shadow-sm">
        <div class="card-body">
            <h3 class="mb-4">Beheer document toegang</h3>

            <?php $form = ActiveForm::begin(['method' => 'post']); ?>
            <!-- documents search + counter + actions -->
            <div class="d-flex gap-2 mb-3 align-items-center">
                <div class="input-group w-50">
                    <input id="doc-search" type="search" class="form-control" placeholder="Zoek documenten...">
                </div>
            </div>

            <div class="ms-auto d-flex justify-content-between align-items-center">
                <span id="checked-counter" class="badge bg-primary mb-2">0 document(s) selected</span>
                <div class="d-flex gap-2">
                    <button type="button" id="select-all-docs" class="btn btn-sm btn-outline-primary rounded-2">Select All</button>
                    <button type="button" id="clear-all-docs" class="btn btn-sm btn-outline-secondary rounded-2">Clear</button>
                </div>
            </div>

            <!-- document cards -->
            <div class="row" id="documents-list">
                <?php foreach ($uploadedDocuments as $doc): ?>
                    <div class="col-md-4 mb-3 doc-card" data-name="<?= Html::encode(mb_strtolower($doc->filename)) ?>">
                        <div class="card h-100 shadow-sm">
                            <div class="card-body d-flex justify-content-between align-items-center w-100">
                                <div class="doc-title"><?= Html::encode($doc->filename) ?></div>
                                <?= Html::checkbox('document_ids[]', false, [
                                    'value' => $doc->id,
                                    'class' => 'form-check-input document-checkbox',
                                    'data-id' => $doc->id,
                                    'aria-label' => 'Select document'
                                ]) ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- permissions section (hidden until documents selected) -->
            <div id="permissions-container" style="display:none;" class="mt-4">
                <h5 class="mb-2">Toewijzing van rechten (van toepassing op geselecteerde documenten)</h5>

                <div class="d-flex gap-2 mb-3 align-items-center">
                    <input id="user-search" class="form-control" placeholder="Search users...">
                </div>

                <div class="table-responsive">
                    <table class="table table-hover rounded table-borderless" id="permissions-table">
                        <thead class="table-light">
                            <tr>
                                <th>User</th>
                                <th style="width:140px" class="text-center">Can View</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                                <tr class="user-row" data-name="<?= Html::encode(mb_strtolower($user->username)) ?>">
                                    <td><?= Html::encode($user->username) ?></td>
                                    <td class="text-center">
                                        <?= Html::checkbox("permissions[{$user->id}][can_view]", false, [
                                            'value' => 1,
                                            'class' => 'form-check-input user-permission-checkbox'
                                        ]) ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <div class="text-end mt-3">
                    <?= Html::submitButton('Save Permissions', ['class' => 'btn btn-success']) ?>
                </div>
            </div>
            <?php ActiveForm::end(); ?>
        </div>
    </div>

    <!-- Extra card to show roles index -->
    <div class="card rounded-3 shadow-sm mt-4">
        <div class="card-body">
            <iframe
                src="<?= \yii\helpers\Url::to(['document-permission/index', 'iframe' => 1]) ?>"
                width="100%"
                height="600px"
                id="roles-iframe"
                class="p-1">
            </iframe>
        </div>
    </div>

</div>

<style>
    .document-checkbox {
        margin: 0;
        /* Remove default margins */
        padding: 0;
        /* Remove default padding */
        height: 20px;
        /* Fixed height for consistency */
        width: 20px;
        /* Fixed width for consistency */
        display: flex;
        /* Use flex to center */
        align-items: center;
        /* Center vertically */
        justify-content: center;
        /* Center horizontally */
    }

    .doc-title {
        font-size: 1rem;
        /* Adjust font size */
        line-height: 1.5;
        /* Match line height for alignment */
    }

    .card-body {
        padding: 1rem;
        /* Consistent padding */
    }
</style>

<?php
$js = <<<JS
// Debounce function to limit how often a function is called
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Function to update the counter of checked checkboxes
function updateCheckedCounter() {
    const checkboxes = document.querySelectorAll('.document-checkbox');
    const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
    const permissionContainer = document.getElementById('permissions-container');
    permissionContainer.style.display = checkedCount > 0 ? 'block' : 'none';
    document.getElementById('checked-counter').textContent = 
        checkedCount + ' document(s) geselecteerd';
}

// Function to filter documents based on search input
function filterDocuments(searchTerm) {
    const docCards = document.querySelectorAll('.doc-card');
    const lowerSearchTerm = searchTerm.toLowerCase();
    docCards.forEach(card => {
        const docName = card.getAttribute('data-name');
        card.style.display = docName.includes(lowerSearchTerm) ? '' : 'none';
    });
}

// Function to filter users based on search input
function filterUsers(searchTerm) {
    const userRows = document.querySelectorAll('.user-row');
    const lowerSearchTerm = searchTerm.toLowerCase();
    userRows.forEach(row => {
        const userName = row.getAttribute('data-name');
        row.style.display = userName.includes(lowerSearchTerm) ? '' : 'none';
    });
}

// Debounced search functions
const debouncedDocSearch = debounce(filterDocuments, 300);
const debouncedUserSearch = debounce(filterUsers, 300);

// Event listener for document search input
document.getElementById('doc-search').addEventListener('input', (e) => {
    debouncedDocSearch(e.target.value);
});

// Event listener for user search input
document.getElementById('user-search').addEventListener('input', (e) => {
    debouncedUserSearch(e.target.value);
});

// Event listener for individual checkbox changes
document.querySelectorAll('.document-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', updateCheckedCounter);
});

// Select All button functionality
document.getElementById('select-all-docs').addEventListener('click', () => {
    document.querySelectorAll('.document-checkbox').forEach(checkbox => {
        checkbox.checked = true;
    });
    updateCheckedCounter();
});

// Clear button functionality
document.getElementById('clear-all-docs').addEventListener('click', () => {
    document.querySelectorAll('.document-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    updateCheckedCounter();
});

// Initialize counter on page load
updateCheckedCounter();
JS;
$this->registerJs($js);
?>